import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  HeadingComponent,
  InputComponent,
} from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { DropdownItem } from '../../components/edit-dialog/edit-dialog.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { SwotDataService } from '../../services/swot-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}



@Component({
  selector: 'app-swot-analysis',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HeadingComponent,
    IconsComponent,
    AweModalComponent,
    InputComponent,
  ],
  templateUrl: './swot-analysis.component.html',
  styleUrl: './swot-analysis.component.scss',
})
export class SwotAnalysisComponent implements OnInit, OnDestroy {
  roboBallIcon: string = 'icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';
  trashIcon: string = 'icons/awe_trash.svg';
  editIcon: string = 'icons/awe_edit.svg';

  private subscription = new Subscription();

  // Accordion State
  activeAccordion: string | null = null;

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to

  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [''];
  regeneratePrompt: string = '';


  dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit', icon: this.editIcon },
    { label: 'Delete', action: 'delete', icon: this.trashIcon },
  ];

  currentEditingFeature: FeatureCard | null = null;
  openDropdownId: string | null = null;

  // sections: FeatureSection[] = [
  //   {
  //     id: 'strengths',
  //     title: 'S',
  //     subtitle: 'STRENGTHS',
  //     features: [
  //       {
  //         id: 'strength-1',
  //         title: 'Unique Biometric Authentication',
  //         description: '',
  //         tags: ['Impact', 'Security', 'Innovation'],
  //         impact: 85,
  //         priority: 90,
  //       },
  //       {
  //         id: 'strength-2',
  //         title: 'Advanced Security Features',
  //         description:
  //           'Multi-layer security with biometric verification and encryption',
  //         tags: ['Security', 'Technology', 'Trust'],
  //         impact: 92,
  //         priority: 88,
  //       },
  //     ],
  //   },
  //   {
  //     id: 'weaknesses',
  //     title: 'W',
  //     subtitle: 'WEAKNESSES',
  //     features: [
  //       {
  //         id: 'weakness-1',
  //         title: 'High Manufacturing Cost',
  //         description:
  //           'Biometric sensors increase production costs significantly',
  //         tags: ['Cost', 'Manufacturing', 'Budget'],
  //         impact: 75,
  //         priority: 80,
  //       },
  //       {
  //         id: 'weakness-2',
  //         title: 'Limited Merchant Adoption',
  //         description:
  //           'Requires specialized terminals for biometric verification',
  //         tags: ['Adoption', 'Infrastructure', 'Market'],
  //         impact: 70,
  //         priority: 85,
  //       },
  //     ],
  //   },
  //   {
  //     id: 'opportunities',
  //     title: 'O',
  //     subtitle: 'OPPORTUNITIES',
  //     features: [
  //       {
  //         id: 'opportunity-1',
  //         title: 'Growing Security Concerns',
  //         description:
  //           'Increasing demand for secure payment methods due to fraud',
  //         tags: ['Market', 'Security', 'Demand'],
  //         impact: 88,
  //         priority: 92,
  //       },
  //       {
  //         id: 'opportunity-2',
  //         title: 'Digital Payment Growth',
  //         description:
  //           'Rapid expansion of contactless payment adoption globally',
  //         tags: ['Growth', 'Digital', 'Global'],
  //         impact: 82,
  //         priority: 78,
  //       },
  //     ],
  //   },
  //   {
  //     id: 'threats',
  //     title: 'T',
  //     subtitle: 'THREATS',
  //     features: [
  //       {
  //         id: 'threat-1',
  //         title: 'Competitive Technology',
  //         description:
  //           'Alternative authentication methods like facial recognition',
  //         tags: ['Competition', 'Technology', 'Innovation'],
  //         impact: 65,
  //         priority: 75,
  //       },
  //       {
  //         id: 'threat-2',
  //         title: 'Privacy Regulations',
  //         description:
  //           'Strict biometric data protection laws may limit adoption',
  //         tags: ['Regulation', 'Privacy', 'Compliance'],
  //         impact: 78,
  //         priority: 82,
  //       },
  //     ],
  //   },
  // ];

  constructor(
    public swotDataService: SwotDataService,
    private pipelineService: ProductPipelineService
  ) {}

  // getSectionIds(): string[] {
  //   return this.sections.map((section) => section.id);
  // }

  onNext(): void {
    // Handle next button click
  }



  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();

    // If clicking on the same dropdown, close it
    if (this.openDropdownId === featureId) {
      this.openDropdownId = null;
    } else {
      // Open the clicked dropdown and close others
      this.openDropdownId = featureId;
    }
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  // Accordion methods for inline expansion
  toggleAccordion(featureId: string): void {
    this.activeAccordion = this.activeAccordion === featureId ? null : featureId;
  }

  isAccordionActive(featureId: string): boolean {
    return this.activeAccordion === featureId;
  }

  clearAccordionSelection(): void {
    this.activeAccordion = null;
  }



  // Method to truncate title for display
  truncateTitle(title: string, maxLength: number = 70): string {
    if (title.length <= maxLength) return title;

    // Try to truncate at sentence boundary first
    const sentences = title.split(/[.!?]+/);
    if (sentences.length > 1 && sentences[0].length <= maxLength) {
      return sentences[0].trim() + '...';
    }

    // Otherwise truncate at word boundary
    const words = title.split(' ');
    let truncated = '';
    for (const word of words) {
      if ((truncated + word).length > maxLength - 3) break;
      truncated += (truncated ? ' ' : '') + word;
    }
    return truncated + '...';
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new SWOT item
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
  }

  onDropdownAction(action: string, feature: FeatureCard): void {
    this.closeAllDropdowns();

    switch (action) {
      case 'edit':
        this.openEditModal(feature);
        break;
      case 'delete':
        this.deleteFeature(feature.id);
        break;
    }
  }

  deleteFeature(featureId: string): void {
    for (const section of this.swotDataService.getSections()) {
      const index = section.features.findIndex((f) => f.id === featureId);
      if (index > -1) {
        section.features.splice(index, 1);
        break;
      }
    }
  }

  updateFeature(): void {
    if (this.isAddingNewFeature) {
      // Add new feature to the specified section
      const section = this.swotDataService.getSectionById(this.currentSectionId);
      if (section) {
        const newFeature: FeatureCard = {
          id: `${this.currentSectionId}-${Date.now()}`,
          title: this.editableFeatureTitle,
          description: this.editableFeatureDescription,
          tags: this.editableFeatureTags.filter((tag) => tag.trim() !== ''),
        };
        section.features.push(newFeature);
      }
    } else if (this.selectedFeatureForEdit) {
      // Update existing feature
      for (const section of this.swotDataService.getSections()) {
        const featureIndex = section.features.findIndex(
          (f) => f.id === this.selectedFeatureForEdit!.id,
        );
        if (featureIndex > -1) {
          section.features[featureIndex] = {
            ...section.features[featureIndex],
            title: this.editableFeatureTitle,
            description: this.editableFeatureDescription,
            tags: this.editableFeatureTags.filter((tag) => tag.trim() !== ''),
          };
          break;
        }
      }
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
  }

  removeEditableTag(index: number): void {
    if (this.editableFeatureTags.length > 1) {
      this.editableFeatureTags.splice(index, 1);
    }
  }

  trackByIndex(index: number): number {
    return index;
  }

  // Method to format tags for display
  formatTag(tag: string): string {
    return tag.split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  // Method to add a new tag
  addTag(): void {
    this.editableFeatureTags.push('');
  }

  // Method to remove a tag
  removeTag(index: number): void {
    this.editableFeatureTags.splice(index, 1);
  }

  // Method to get section name for modal title
  getSectionName(): string {
    if (this.isAddingNewFeature) {
      const section = this.swotDataService.getSectionById(this.currentSectionId);
      return section ? section.subtitle : 'SWOT';
    } else if (this.selectedFeatureForEdit) {
      // Find which section this feature belongs to
      for (const section of this.swotDataService.getSections()) {
        if (section.features.some(f => f.id === this.selectedFeatureForEdit!.id)) {
          return section.subtitle;
        }
      }
    }
    return 'SWOT';
  }

  // Method to open modal from card menu
  openDialogFromCard(feature: FeatureCard): void {
    this.openEditModal(feature);
  }

  ngOnInit(): void {
    // Add sample data for testing if no data exists
    if (this.swotDataService.getSections().length === 0) {
      const sampleData = {
        strengths: [
          {
            title: 'Unique Biometric Authentication',
            description: 'Advanced fingerprint and facial recognition technology provides superior security compared to traditional payment methods.',
            justification: 'This technology differentiates our product in the market'
          },
          {
            title: 'Strong Brand Recognition',
            description: 'Well-established brand with high customer trust and loyalty in the financial services sector.',
            justification: 'Brand strength provides competitive advantage'
          }
        ],
        weaknesses: [
          {
            title: 'High Manufacturing Cost',
            description: 'Biometric sensors and advanced security features significantly increase production costs.',
            justification: 'Cost structure impacts profitability and pricing strategy'
          }
        ],
        opportunities: [
          {
            title: 'Growing Security Concerns',
            description: 'Increasing demand for secure payment methods due to rising fraud incidents.',
            justification: 'Market trend favors our security-focused approach'
          }
        ],
        threats: [
          {
            title: 'Competitive Technology',
            description: 'Alternative authentication methods like facial recognition and voice recognition are emerging.',
            justification: 'Technology evolution could make our approach obsolete'
          }
        ]
      };
      this.swotDataService.updateFromApiResponse(sampleData);
    }

    // Subscribe to pipeline state changes to load SWOT data from API
    this.subscription.add(
      this.pipelineService.pipelineState$.subscribe((state) => {

        // Load SWOT data whenever it's available (regardless of current_step)
        // This ensures data restoration after page reload
        if (state.data.swot) {
          this.swotDataService.updateFromApiResponse(state.data.swot);
        }
      })
    );

    // Add click listener to close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown')) {
        this.closeAllDropdowns();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }


}
