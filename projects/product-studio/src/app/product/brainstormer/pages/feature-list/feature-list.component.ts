import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { InputComponent } from '@awe/play-comp-library';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { FeatureDataService, FeatureCard, FeatureSection } from '../../services/export-service/feature-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    AweModalComponent,
    InputComponent,
  ],
  templateUrl: './feature-list.component.html',
  styleUrls: ['./feature-list.component.scss'], // Corrected property name
})
export class FeatureListComponent implements OnInit, OnDestroy {
  // Subscription management
  private subscription = new Subscription();

  roboBallIcon: string = 'icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to
  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [];
  regeneratePrompt: string = '';

  // Expansion Modal State
  isExpansionModalOpen = false;
  expandedFeature: FeatureCard | null = null;

  openDropdownId: string | null = null;

  sections: FeatureSection[] = [];

  constructor(
    private cdRef: ChangeDetectorRef,
    private featureDataService: FeatureDataService,
    private pipelineService: ProductPipelineService
  ) {}

  ngOnInit(): void {

    // Subscribe to data changes from service FIRST to ensure we catch all updates
    this.subscription.add(
      this.featureDataService.sections$.subscribe(
        (sections) => {

            id: sections.id,
            title: sections.title,
            featureCount: sections.features.length,
            features: sections.features.map(f => ({ id: f.id, title: f.title }))
          })));

          this.sections = sections;
          this.cdRef.detectChanges(); // Force change detection

            sections.reduce((total, section) => total + section.features.length, 0));
        }
      )
    );

    // Subscribe to pipeline state changes to load feature data from API
    this.subscription.add(
      this.pipelineService.pipelineState$.subscribe((state) => {
        
        if (state?.data?.features) {
          
          // Log detailed feature data structure
          const features = state.data.features;
          if (Array.isArray(features)) {
              total: features.length,
              byRank: features.reduce((acc, f) => {
                const rank = (f.moscow_rank || '').toLowerCase();
                acc[rank] = (acc[rank] || 0) + 1;
                return acc;
              }, {} as Record<string, number>)
            });
          } else if (typeof features === 'object') {
              must_have: features.must_have?.length || 0,
              should_have: features.should_have?.length || 0,
              could_have: features.could_have?.length || 0,
              wont_have: features.wont_have?.length || 0
            });
          }

          this.featureDataService.updateFromApiResponse(state.data.features);
        } else {
        }
      })
    );

    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        this.closeAllDropdowns();
      }
    });

    // TEMPORARY: Load sample data for testing if no data is available after 2 seconds
    setTimeout(() => {
      if (this.sections.length === 0 || this.sections.every(section => section.features.length === 0)) {
        this.featureDataService.loadSampleData();
      }
    }, 2000);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getSectionIds(): string[] {
    return this.featureDataService.getSectionIds();
  }

  getTotalFeatureCount(): number {
    return this.sections.reduce((total, section) => total + section.features.length, 0);
  }

  /**
   * Manual method to refresh feature data from pipeline state
   */
  refreshFeatureData(): void {
    
    // Check current sections state
      id: s.id,
      title: s.title,
      featureCount: s.features.length
    })));

    const currentState = this.pipelineService.currentState;
    
    if (currentState?.data?.features) {
      const features = currentState.data.features;

      // Log detailed feature analysis
        type: Array.isArray(features) ? 'array' : typeof features,
        totalFeatures: Array.isArray(features) ? features.length : 
          Object.values(features).reduce((sum, arr) => sum + (Array.isArray(arr) ? arr.length : 0), 0),
        wontHaveFeatures: Array.isArray(features) ? 
          features.filter(f => (f.moscow_rank || '').toLowerCase().includes('wont')).length :
          features.wont_have?.length || 0
      });

      this.featureDataService.updateFromApiResponse(features);
    } else {
      this.featureDataService.loadSampleData();
    }
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      // Reorder within same section
      const sectionId = event.container.id;
      this.featureDataService.reorderFeatures(sectionId, event.previousIndex, event.currentIndex);
    } else {
      // Move between sections
      const fromSectionId = event.previousContainer.id;
      const toSectionId = event.container.id;
      const featureId = event.previousContainer.data[event.previousIndex].id;

      this.featureDataService.moveFeature(featureId, fromSectionId, toSectionId, event.currentIndex);
    }
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new feature
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  deleteFeature(_sectionId: string, featureId: string): void {
    this.featureDataService.deleteFeature(featureId);
    this.closeAllDropdowns();
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === featureId ? null : featureId;
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [];
    this.regeneratePrompt = '';
  }

  // Card Click Handler
  onCardClick(featureId: string, event: Event): void {
    // Check if the click target is the three-dot menu or dropdown
    const target = event.target as HTMLElement;
    const isThreeDotMenu = target.closest('.three-dot-menu');
    const isDropdown = target.closest('.dropdown-arrow');

    // Only expand if not clicking on three-dot menu or dropdown
    if (!isThreeDotMenu && !isDropdown) {
      // Small delay to distinguish between click and drag start
      setTimeout(() => {
        if (!this.isDragging) {
          this.expandCard(featureId);
        }
      }, 50);
    }
  }

  // Track dragging state
  private isDragging = false;

  // Drag event handlers
  onDragStarted(): void {
    this.isDragging = true;
  }

  onDragEnded(): void {
    // Reset dragging state after a short delay
    setTimeout(() => {
      this.isDragging = false;
    }, 100);
  }

  // Expansion Modal Methods
  expandCard(featureId: string): void {
    // Find the feature across all sections
    for (const section of this.sections) {
      const feature = section.features.find(f => f.id === featureId);
      if (feature) {
        this.expandedFeature = feature;
        this.isExpansionModalOpen = true;
        // Close any open dropdowns when expanding
        this.closeAllDropdowns();
        break;
      }
    }
  }

  closeExpansionModal(): void {
    this.isExpansionModalOpen = false;
    this.expandedFeature = null;
  }

  // Tag formatting method
  formatTag(tag: string): string {
    return tag.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }

  // Keyboard event handler
  onKeyDown(event: KeyboardEvent, action: () => void): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  }

  updateFeature(): void {
    // Validate required fields
    if (!this.editableFeatureTitle.trim()) {
      alert('Please enter a feature title');
      return;
    }

    if (this.isAddingNewFeature) {
      // Adding new feature via service
      this.featureDataService.addFeature(this.currentSectionId, {
        title: this.editableFeatureTitle.trim(),
        description: this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter((tag) => tag.trim() !== '')
          .map((tag) => tag.trim()),
      });
    } else {
      // Updating existing feature via service
      if (!this.selectedFeatureForEdit) return;

      this.featureDataService.updateFeature(this.selectedFeatureForEdit.id, {
        title: this.editableFeatureTitle.trim(),
        description: this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter((tag) => tag.trim() !== '')
          .map((tag) => tag.trim()),
      });
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-tag-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableTag(index: number): void {
    this.editableFeatureTags.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteFeatureFromCard(sectionId: string, featureId: string) {
    this.deleteFeature(sectionId, featureId);
    this.closeAllDropdowns();
  }
}
