import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectorRef, Component, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { Subscription } from 'rxjs';
import { distinctUntilChanged, debounceTime } from 'rxjs/operators';
// Removed awe component imports - using custom components instead
import {
  UnderstandingDataService,
  CanvasItem,
} from '../../services/understanding-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { LBCData } from '../../interfaces/pipeline-api.interface';
import { SplitScreenService } from '../../services/split-screen.service';

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
  ],
})
export class UnderstandingComponent implements OnInit, OnDestroy {
  // Action Icons (used in the projected header)
  trashIcon: string = 'icons/awe_trash.svg';
  editIcon: string = 'icons/awe_edit.svg';
  errorIcon: string = 'icons/awe_error.svg';

  // Subscription management
  private subscription = new Subscription();

  // Card Data Icons (used in the projected header)
  problemIcon: string = 'cards-icons/problem.svg';
  keyPartnerCardImg: string = 'cards-icons/key-partners.svg';
  valuePropositionCardImg: string = 'cards-icons/revenue-streams.svg';
  solutionCardImg: string = 'cards-icons/solution.svg';
  customerSegmentsCardImg: string = 'cards-icons/customer-segments.svg';
  keyMetricsCardImg: string = 'cards-icons/key-metrics.svg';
  alternativesCardImg: string = 'cards-icons/alternatives.svg';
  costStructureCardImg: string = 'cards-icons/cost-structure.svg';
  revenueStreamsCardImg: string = 'cards-icons/revenue-streams.svg';
  solutionTenantsCardImg: string = 'cards-icons/solutionTenants.png';

  button_bg: string =
    'linear-gradient(90deg, rgba(101, 102, 205, 0.40) -2.03%, rgba(249, 108, 171, 0.40) 109.39%);';
  loadingStates: Record<string, boolean> = {
    submit: false,
    save: false,
    delete: false,
    skeleton1: false,
    skeleton2: false,
  };

  // Main data source for the cards
  businessModelCanvas: CanvasItem[] = [];

  // Cached data properties to prevent continuous re-rendering
  private _cachedFirstRowProblemCard: CanvasItem[] = [];
  private _cachedFirstRowSolutionCard: CanvasItem[] = [];
  private _cachedKeyPartnersData: CanvasItem[] = [];
  private _cachedValuePropositionData: CanvasItem[] = [];
  private _cachedKeyMetricsData: CanvasItem[] = [];
  private _cachedAlternativesData: CanvasItem[] = [];
  private _cachedSolutionTenantsData: CanvasItem[] = [];
  private _cachedCustomerSegmentItems: CanvasItem[] = [];
  private _cachedCostRevenueItems: CanvasItem[] = [];
  

  // Modal State
  isEditModalOpen = false;
  selectedItemForEdit: CanvasItem | null = null;
  editableItemData: string[] = [];
  regeneratePrompt: string = '';

  constructor(
    private cdRef: ChangeDetectorRef,
    private understandingDataService: UnderstandingDataService,
    private pipelineService: ProductPipelineService,
    private splitScreenService: SplitScreenService
  ) {}

  ngOnInit(): void {
    // Initialize cached data
    this.updateCachedData();

    // Add temporary mock data for testing icons
    this.loadMockDataForTesting();

    // Subscribe to data changes with debouncing and distinct checking
    this.subscription.add(
      this.understandingDataService.businessModelCanvas$
        .pipe(
          debounceTime(50), // Small debounce to batch rapid updates
          distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
        )
        .subscribe((canvas) => {
          this.businessModelCanvas = canvas;
          this.updateIconsForCanvas();
          this.updateCachedData();
          this.cdRef.markForCheck(); // Trigger change detection only when needed
        }),
    );

    // Subscribe to pipeline state changes to load appropriate data
    this.subscription.add(
      this.pipelineService.pipelineState$
        .pipe(
          debounceTime(100), // Debounce pipeline state changes
          distinctUntilChanged((prev, curr) =>
            prev.current_step === curr.current_step &&
            JSON.stringify(prev.data) === JSON.stringify(curr.data)
          )
        )
        .subscribe((state) => {

        // Check if we have market research data (step: "market_research" with data)
        if (
          state.current_step === 'market_research' &&
          state.data &&
          Object.keys(state.data).length > 0
        ) {
          // Load market research data from API response
          this.loadMarketResearchData(state.data);
        } else if (state.data.lbc) {
          // Load LBC data from API response (for future LBC step)
          this.loadLBCData(state.data.lbc);
        } else if (state.run_id && state.current_step === 'market_research') {
          // Wait for API data - no more mock data loading
        }

        // Trigger change detection after processing pipeline state
        this.cdRef.markForCheck();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Mock data loading method commented out - now using only API data
  // /**
  //  * Load initial data for understanding step
  //  */
  // private loadInitialData(): void {
  //   // For now, show default data until we get the actual market research data
  //   // In a real implementation, you might want to call the API here
  //   // this.pipelineService.progressToStep('market_research').subscribe(...)

  //   // Use the default data from the service for now
  //   // The actual market research data will be loaded when user clicks next
  // }

  /**
   * Load mock data for testing icons - temporary method
   */
  private loadMockDataForTesting(): void {
    const mockCanvasItems: CanvasItem[] = [
      {
        id: 'problem',
        title: 'Problem',
        icon: this.problemIcon,
        iconBg: '#ffebee',
        data: ['Sample problem 1', 'Sample problem 2'],
      },
      {
        id: 'key-partners',
        title: 'Key Partners',
        icon: this.keyPartnerCardImg,
        iconBg: '#e3f2fd',
        data: ['Partner 1', 'Partner 2'],
      },
      {
        id: 'value-proposition',
        title: 'Value Proposition',
        icon: this.valuePropositionCardImg,
        iconBg: '#fff3e0',
        data: ['Value prop 1', 'Value prop 2'],
      },
      {
        id: 'customer-segments',
        title: 'Customer Segments',
        icon: this.customerSegmentsCardImg,
        iconBg: '#e8f5e8',
        data: ['Segment 1', 'Segment 2'],
      },
      {
        id: 'key-metrics',
        title: 'Key Metrics',
        icon: this.keyMetricsCardImg,
        iconBg: '#fff3e0',
        data: ['Metric 1', 'Metric 2'],
      },
      {
        id: 'solution',
        title: 'Solution',
        icon: this.solutionCardImg,
        iconBg: '#e8f5e8',
        data: ['Solution 1', 'Solution 2'],
      },
      {
        id: 'alternatives',
        title: 'Alternatives',
        icon: this.alternativesCardImg,
        iconBg: '#e8f5e8',
        data: ['Alternative 1', 'Alternative 2'],
      },
      {
        id: 'cost-structure',
        title: 'Cost Structure',
        icon: this.costStructureCardImg,
        iconBg: '#ffebee',
        data: ['Cost 1', 'Cost 2'],
      },
      {
        id: 'revenue-streams',
        title: 'Revenue Streams',
        icon: this.revenueStreamsCardImg,
        iconBg: '#e8f5e8',
        data: ['Revenue 1', 'Revenue 2'],
      },
      {
        id: 'solution-tenants',
        title: 'Solution Tenants',
        icon: this.solutionTenantsCardImg,
        iconBg: '#e8f5e8',
        data: ['Tenant 1', 'Tenant 2'],
      },
    ];

    // Update the understanding data service with mock data
    this.understandingDataService.updateBusinessModelCanvas(mockCanvasItems);
  }

  /**
   * Load Market Research data from pipeline API response
   * This handles the new API response structure with LBC data
   */
  private loadMarketResearchData(apiData: any): void {

  }

  /**
   * Load LBC data from pipeline API response
   */
  private loadLBCData(lbcData: LBCData): void {
    const lbcCanvasItems: CanvasItem[] = [
      {
        id: 'problem',
        title: 'Problem',
        icon: this.problemIcon,
        iconBg: '#ffebee',
        data: lbcData.problem || [],
      },
      {
        id: 'key-partners',
        title: 'Key Partners',
        icon: this.keyPartnerCardImg,
        iconBg: '#e3f2fd',
        data: lbcData.key_partners || [],
      },
      {
        id: 'value-proposition',
        title: 'Value Proposition',
        icon: this.valuePropositionCardImg,
        iconBg: '#fff3e0',
        data: lbcData.value_proposition || [],
      },
      {
        id: 'customer-segments',
        title: 'Customer Segments',
        icon: this.customerSegmentsCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.customer_segments || [],
      },
      {
        id: 'key-metrics',
        title: 'Key Metrics',
        icon: this.keyMetricsCardImg,
        iconBg: '#fff3e0',
        data: lbcData.key_metrics || [],
      },
      {
        id: 'solution',
        title: 'Solution',
        icon: this.solutionCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.solution || [],
      },
      {
        id: 'alternatives',
        title: 'Alternatives',
        icon: this.alternativesCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.alternatives || [],
      },
      {
        id: 'cost-structure',
        title: 'Cost Structure',
        icon: this.costStructureCardImg,
        iconBg: '#ffebee',
        data: lbcData.cost_structure || [],
      },
      {
        id: 'revenue-streams',
        title: 'Revenue Streams',
        icon: this.revenueStreamsCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.revenue_streams || [],
      },
      {
        id: 'solution-tenants',
        title: 'Solution Tenants',
        icon: this.solutionCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.solution_tenants || [],
      },
    ];

    // Update the understanding data service with LBC data
    this.understandingDataService.updateBusinessModelCanvas(lbcCanvasItems);
  }

  private updateIconsForCanvas(): void {
    // Update icons for canvas items based on their IDs
    this.businessModelCanvas.forEach((item) => {
      switch (item.id) {
        case 'problem':
          item.icon = this.problemIcon;
          break;
        case 'key-partners':
          item.icon = this.keyPartnerCardImg;
          break;
        case 'value-proposition':
          item.icon = this.valuePropositionCardImg;
          break;
        case 'solution':
          item.icon = this.solutionCardImg;
          break;
        case 'customer-segments':
          item.icon = this.customerSegmentsCardImg;
          break;
        case 'key-metrics':
          item.icon = this.keyMetricsCardImg;
          break;
        case 'alternatives':
          item.icon = this.alternativesCardImg;
          break;
        case 'cost-structure':
          item.icon = this.costStructureCardImg;
          break;
        case 'revenue-streams':
          item.icon = this.revenueStreamsCardImg;
          break;
      }
    });
  }

  private updateCachedData(): void {
    // Update cached data to prevent continuous re-rendering
    this._cachedFirstRowProblemCard = this.understandingDataService.getFirstRowProblemData();
    this._cachedFirstRowSolutionCard = this.understandingDataService.getFirstRowSolutionCard();
    this._cachedKeyPartnersData = this.understandingDataService.getKeyPartnersData();
    this._cachedValuePropositionData = this.understandingDataService.getValuePropositionData();
    this._cachedKeyMetricsData = this.understandingDataService.getKeyMetricsAlternativesData();
    this._cachedAlternativesData = this.understandingDataService.getAlternativesData();
    this._cachedCustomerSegmentItems = this.understandingDataService.getCustomerSegmentItems();
    this._cachedCostRevenueItems = this.understandingDataService.getCostRevenueItems();

    const solutionTenantsItem = this.understandingDataService.getCanvasItemById('solution-tenants');
    this._cachedSolutionTenantsData = solutionTenantsItem ? [solutionTenantsItem] : [];

    // Trigger change detection
    this.cdRef.markForCheck();
  }

  get firstRowProblemCard(): CanvasItem[] {
    return this._cachedFirstRowProblemCard;
  }
  get firstRowSolutionCard(): CanvasItem[] {
    return this._cachedFirstRowSolutionCard;
  }

  get keyPartnersData(): CanvasItem[] {
    return this._cachedKeyPartnersData;
  }

  get valuePropositionData(): CanvasItem[] {
    return this._cachedValuePropositionData;
  }

  get keyMetricsData(): CanvasItem[] {
    return this._cachedKeyMetricsData;
  }
  get alternativesData(): CanvasItem[] {
    return this._cachedAlternativesData;
  }

  get solutionTenantsData(): CanvasItem[] {
    return this._cachedSolutionTenantsData;
  }

  get customerSegmentItems(): CanvasItem[] {
    return this._cachedCustomerSegmentItems;
  }

  get costRevenueItems(): CanvasItem[] {
    return this._cachedCostRevenueItems;
  }

  // Helper methods to check if cards have meaningful data
  hasCardData(item: CanvasItem): boolean {
    return item && item.data && item.data.length > 0 &&
           item.data.some(dataItem => dataItem && dataItem.trim().length > 0);
  }

  /**
   * Trims text data to display only the first sentence (up to the first period)
   * @param text - The text to trim
   * @returns The trimmed text ending with a period
   */
  trimToFirstSentence(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    const trimmedText = text.trim();
    if (trimmedText.length === 0) {
      return '';
    }

    // Find the first period
    const firstPeriodIndex = trimmedText.indexOf('.');

    if (firstPeriodIndex === -1) {
      // No period found, return the original text with a period added
      return trimmedText + '.';
    }

    // Return text up to and including the first period
    return trimmedText.substring(0, firstPeriodIndex + 1);
  }

  /**
   * Trims all data items in a CanvasItem to first sentences
   * @param item - The CanvasItem to process
   * @returns A new CanvasItem with trimmed data
   */
  getTrimmedCanvasItem(item: CanvasItem): CanvasItem {
    if (!item || !item.data) {
      return item;
    }

    return {
      ...item,
      data: item.data.map(dataItem => this.trimToFirstSentence(dataItem))
    };
  }

  /**
   * Trims all data items in an array of CanvasItems to first sentences
   * @param items - The array of CanvasItems to process
   * @returns A new array with trimmed data
   */
  getTrimmedCanvasItems(items: CanvasItem[]): CanvasItem[] {
    if (!items || !Array.isArray(items)) {
      return [];
    }

    return items.map(item => this.getTrimmedCanvasItem(item));
  }

  get costRevenueItemsWithData(): CanvasItem[] {
    return this.costRevenueItems.filter(item => this.hasCardData(item));
  }

  get customerSegmentItemsWithData(): CanvasItem[] {
    return this.customerSegmentItems.filter(item => this.hasCardData(item));
  }

  get solutionTenantsDataWithData(): CanvasItem[] {
    return this.solutionTenantsData.filter(item => this.hasCardData(item));
  }

  get firstRowProblemCardWithData(): CanvasItem[] {
    const filteredItems = this.firstRowProblemCard.filter(item => this.hasCardData(item));
    return this.getTrimmedCanvasItems(filteredItems);
  }

  get firstRowSolutionCardWithData(): CanvasItem[] {
    const filteredItems = this.firstRowSolutionCard.filter(item => this.hasCardData(item));
    return this.getTrimmedCanvasItems(filteredItems);
  }

  get keyPartnersDataWithData(): CanvasItem[] {
    return this.keyPartnersData.filter(item => this.hasCardData(item));
  }

  get valuePropositionDataWithData(): CanvasItem[] {
    return this.valuePropositionData.filter(item => this.hasCardData(item));
  }

  get keyMetricsDataWithData(): CanvasItem[] {
    return this.keyMetricsData.filter(item => this.hasCardData(item));
  }

  get alternativesDataWithData(): CanvasItem[] {
    return this.alternativesData.filter(item => this.hasCardData(item));
  }

  // Action handlers
  onEdit(item: CanvasItem): void {
    this.openEditModal(item);
  }

  onDelete(item: CanvasItem): void {
    // Implement delete functionality if needed
  }

  // Modal Methods
  openEditModal(item: CanvasItem): void {
    this.selectedItemForEdit = item;
    this.editableItemData = [...item.data];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;

    // Notify service that an edit modal is open
    // this.splitScreenService.setEditModalOpen(true);

    // Close split screen to show full-screen modal view
    this.splitScreenService.closeSplitScreen();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedItemForEdit = null;
    this.editableItemData = [];
    this.regeneratePrompt = '';

    // Notify service that edit modal is closed
    // this.splitScreenService.setEditModalOpen(false);

    // Restore split screen when modal closes
    this.splitScreenService.openSplitScreen();

    this.cdRef.detectChanges();
  }

  updateUnderstandingItem(event: Event): void {
    if (this.selectedItemForEdit) {
      // Update via service
      this.understandingDataService.updateCanvasItem(
        this.selectedItemForEdit.id,
        [...this.editableItemData],
      );

      // Handle regenerate prompt
      if (this.regeneratePrompt) {
        this.loadingStates['submit'] = true;
        setTimeout(() => {
          this.loadingStates['submit'] = false;
        }, 2000);
        this.regeneratePrompt = '';
      }
      this.closeEditModal();
    }
  }

  // Methods for managing editableItemData in the modal
  addEditableDataItem(): void {
    this.editableItemData.push('');
    this.cdRef.detectChanges();
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-data-item-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableDataItem(index: number): void {
    this.editableItemData.splice(index, 1);
  }

  /**
   * TrackBy function for ngFor loops to optimize change detection
   * This prevents unnecessary re-rendering of list items
   */
  trackByItemId(_index: number, item: CanvasItem): string {
    return item.id;
  }

  /**
   * TrackBy function for data items within canvas items
   */
  trackByDataIndex(index: number, _item: string): number {
    return index;
  }

  // Keep the existing trackByFn for backward compatibility
  trackByFn(index: number, _item: any): any {
    return index;
  }
}
