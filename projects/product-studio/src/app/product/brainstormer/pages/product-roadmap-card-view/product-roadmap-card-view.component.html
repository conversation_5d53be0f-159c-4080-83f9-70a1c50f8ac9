<div class="container-fluid" (click)="closeAllDropdowns()">
  <div class="row g-0">
    <!-- g-3 provides gap between columns -->
    <div
      *ngFor="let quarter of quarters"
      class="col-12 col-sm-6 col-lg-3 d-flex flex-column p-2"
    >
      <div class="pt-2">
        <div class="roadmap-card-main px-4 pt-2">
          <!-- Quarter Header -->
          <div
            class="section-header px-3 pt-3"
            [style]="`border-bottom: 3px solid ${quarter.qurterColor}`"
          >
            <!-- Child 1: The quarter title -->
            <div class="section-title">
              <span
                class="bullet-pt"
                [style]="`background-color: ${quarter.qurterColor}`"
              ></span>
              <awe-heading variant="s2" type="bold">{{
                quarter.title
              }}</awe-heading>
              <span class="task-count-badge">{{ quarter.tasks.length }}</span>
            </div>
            <!-- Child 2: The quarter number -->
            <div class="section-action">
              <awe-icons
                iconName="awe_plus"
                (click)="addNewRoadmapCard(quarter.id)"
                class="plus-icon"
              ></awe-icons>
            </div>
          </div>
          <!-- Drop Zone for Tasks -->
          <div
            cdkDropList
            [id]="quarter.id"
            [cdkDropListData]="quarter.tasks"
            [cdkDropListConnectedTo]="getSectionIds()"
            (cdkDropListDropped)="onDrop($event)"
            [attr.data-quarter-id]="quarter.id"
            class="roadmap-card-list-dropzone border border-top-0 d-flex flex-column gap-3 flex-grow-1"
          >
            <!-- Task Cards using awe-card -->
            <awe-card
              *ngFor="let task of quarter.tasks"
              [showHeader]="true"
              [showBody]="true"
              [applyHeaderPadding]="true"
              [applyBodyPadding]="true"
              cardClass="roadmap-card-item-card"
              cdkDrag
              class="pt-3"
              (cdkDragStarted)="
                $event.source.element.nativeElement.style.cursor = 'grabbing'
              "
              (cdkDragEnded)="
                $event.source.element.nativeElement.style.cursor = 'grab'
              "
            >
              <!-- Projected Header for awe-card -->
              <div
                awe-card-header-content
                class="d-flex justify-content-between align-items-start p-3"
              >
                <!-- Left side: Priority and Task Title -->
                <div class="d-flex flex-column flex-grow-1">
                  <!-- Priority Tag -->
                  <span
                    class="roadmap-card-tag rounded-pill px-2 py-1 d-inline-block text-capitalize mb-2 align-self-start"
                    [style.color]="getPriorityColor(task.priority).color"
                    [style.background]="
                      getPriorityColor(task.priority).background
                    "
                  >
                    {{ task.priority }}
                  </span>

                  <!-- Task Title -->
                  <awe-heading
                    variant="s2"
                    type="bold"
                    class="roadmap-card-title mb-0"
                    >{{ task.task }}</awe-heading
                  >
                </div>

                <!-- Right side: Three-dot menu -->
                <div class="position-relative">
                  <awe-icons
                    (click)="toggleDropdown(task.id, $event)"
                    [iconName]="'three-dot-vertical'"
                    [iconColor]="'blue'"
                    class="three-dot-icon ms-2"
                  ></awe-icons>

                  <div class="dropdown-arrow position-absolute">
                    <div
                      class="dropdown-menu dropdown-menu-end"
                      [class.show]="isDropdownOpen(task.id)"
                    >
                      <button
                        class="dropdown-item border-buttom"
                        type="button"
                        (click)="openEditModal(task)"
                      >
                        Edit
                      </button>
                      <button
                        class="dropdown-item text-danger"
                        type="button"
                        (click)="
                          deleteRoadmapCard(task.id); closeAllDropdowns()
                        "
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div awe-card-body-content class="px-3 pb-3">
                <awe-body-text
                  type="body-text"
                  class="roadmap-card-description text-muted small mb-3"
                >
                  {{ task.description }}
                </awe-body-text>
              </div>
              <!-- End Date -->
              <div
                class="time-zone px-3 mb-2 d-flex align-items-center justify-content-end text-muted small"
              >
                <awe-icons
                  iconName="awe_timer"
                  iconSize="14px"
                  class="me-1"
                ></awe-icons>
                <span>{{ formatDate(task.endDate) }}</span>
              </div>
            </awe-card>

            <!-- Empty State for Drop Zone -->
            <div
              *ngIf="quarter.tasks.length === 0"
              class="text-center text-muted fst-italic py-4"
            >
              Drag and drop tasks here
            </div>
          </div>

          <!-- Add More Button -->
          <!-- <div class="add-more-section mt-3 text-center p-3">
            <button
              class="add-more-btn w-100"
              (click)="addNewRoadmapCard(quarter.id)"
            >
              Add more
              <awe-icons iconName="awe_plus" class="mt-2"></awe-icons>
            </button>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Task Modal -->
<awe-modal
  [isOpen]="isEditModalOpen"
  (closed)="closeEditModal()"
  [showHeader]="true"
  [showFooter]="true"
  width="600px"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="edit-roadmap-card-modal"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="bold" class="modal-title mb-0">
      Edit Roadmap Task
    </awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <!-- Task Title -->
    <div class="col-md-12 inp-container">
      <div class="label">
        <label for="taskTitle">Task Title:</label>
      </div>
      <div class="input-wrapper">
        <awe-input
          id="taskTitle"
          type="text"
          variant="fluid"
          [(ngModel)]="editableRoadmapCardTitle"
          placeholder="Enter task title"
          class="w-100"
        ></awe-input>
      </div>
    </div>

    <!-- Task Description -->
    <div class="col-md-12 mt-2">
      <div>
        <label for="name">Description:</label>
      </div>
      <div class="input-wrapper">
        <awe-input
          variant="fluid"
          [expand]="true"
          id="taskDescription"
          [(ngModel)]="editableRoadmapCardDescription"
          placeholder="Enter description"
          class="w-100"
        ></awe-input>
      </div>
    </div>

    <!-- Priority Selection -->
   <div class="col-md-12 inp-container mt-2">
      <div class="label">
        <label for="taskPriority">Priority:</label>
      </div>
      <div class="input-wrapper">
        <awe-dropdown
          [selectedValue]="editableRoadmapCardPriority"
          [options]="priorities"
          animation="rotateX"
          theme="light"
        ></awe-dropdown>
      </div>
    </div>

    <!-- Start Date & End Date -->
    <div class="col-md-12 inp-container mt-2">
      <div class="label">
        <label for="taskStartDate">Start Date and End date:</label>
      </div>
      <div class="input-wrapper">
        <awe-datepicker
          [size]="'large'"
          [range]="true"
          (dateRangeSelected)="onRangeSelected($event)"
          [(ngModel)]="editableRoadmapCardStartDate"
        ></awe-datepicker>
      </div>
    </div>

    <!-- Regenerate Section -->
    <div class="regenerate-section mt-3">
      <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>
      <awe-input
        id="regeneratePrompt"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="What would you like to change?"
        (iconClickEvent)="onRegenerate()"
        class="mb-2"
      >
      </awe-input>
    </div>
  </div>

  <!-- Projected Modal Footer -->
 <div awe-modal-footer>
      <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
        <button
          type="button"
          class="btn-cancel px-5"
          (click)="closeEditModal()"
        >
          Cancel
        </button>
        <button type="button" class="btn-delete px-5" (click)="updateRoadmapCard()">
          Update
        </button>
      </div>
    </div>
</awe-modal>
