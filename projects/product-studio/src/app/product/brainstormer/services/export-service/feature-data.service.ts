import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
  tenant?: string;
  justification?: string;
  moscow_rank?: string;
}

export interface FeatureSection {
  id: string;
  title: string; // 'Mo', 'S', 'Co', 'W'
  subtitle: string; // 'MUST HAVE', 'SHOULD HAVE', etc.
  features: FeatureCard[];
}

@Injectable({
  providedIn: 'root'
})
export class FeatureDataService {
  // Initialize with empty array - data will come from API
  private sectionsSubject = new BehaviorSubject<FeatureSection[]>([]);
  public sections$ = this.sectionsSubject.asObservable();

  constructor() {}

  /**
   * Load sample data for testing purposes
   */
  loadSampleData(): void {
    const sampleData = {
      must_have: [
        {
          title: "User Authentication",
          description: "Secure login and registration system",
          justification: "Essential for user security and personalization"
        },
        {
          title: "Core Dashboard",
          description: "Main interface for users to access key features",
          justification: "Primary user interaction point"
        }
      ],
      should_have: [
        {
          title: "Push Notifications",
          description: "Real-time alerts and updates",
          justification: "Improves user engagement and retention"
        },
        {
          title: "Data Export",
          description: "Allow users to export their data",
          justification: "User data ownership and portability"
        }
      ],
      could_have: [
        {
          title: "Dark Mode",
          description: "Alternative UI theme for low-light usage",
          justification: "Enhanced user experience preference"
        }
      ],
      wont_have: [
        {
          title: "Advanced Analytics",
          description: "Complex data analysis features",
          justification: "Out of scope for initial release"
        }
      ]
    };

    this.updateFromApiResponse(sampleData);
  }

  // Get all sections
  getSections(): FeatureSection[] {
    return this.sectionsSubject.value;
  }

  // Get section by ID
  getSectionById(id: string): FeatureSection | undefined {
    return this.sectionsSubject.value.find(section => section.id === id);
  }

  // Get section IDs for drag-drop
  getSectionIds(): string[] {
    return this.sectionsSubject.value.map(section => section.id);
  }

  // Add new feature to a section
  addFeature(sectionId: string, feature: Omit<FeatureCard, 'id'>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(section => section.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const newFeature: FeatureCard = {
        ...feature,
        id: `${sectionId}-feature-${Date.now()}`
      };

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features: [...updatedSections[sectionIndex].features, newFeature]
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Update existing feature
  updateFeature(featureId: string, updatedFeature: Partial<FeatureCard>): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: [
            ...updatedSections[sectionIndex].features.slice(0, featureIndex),
            { ...updatedSections[sectionIndex].features[featureIndex], ...updatedFeature },
            ...updatedSections[sectionIndex].features.slice(featureIndex + 1)
          ]
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Delete feature
  deleteFeature(featureId: string): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: updatedSections[sectionIndex].features.filter(f => f.id !== featureId)
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Move feature between sections (for drag-drop)
  moveFeature(featureId: string, fromSectionId: string, toSectionId: string, newIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    // Find and remove feature from source section
    const fromSectionIndex = updatedSections.findIndex(s => s.id === fromSectionId);
    const toSectionIndex = updatedSections.findIndex(s => s.id === toSectionId);

    if (fromSectionIndex !== -1 && toSectionIndex !== -1) {
      const featureIndex = updatedSections[fromSectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        const [movedFeature] = updatedSections[fromSectionIndex].features.splice(featureIndex, 1);
        updatedSections[toSectionIndex].features.splice(newIndex, 0, movedFeature);

        this.sectionsSubject.next(updatedSections);
      }
    }
  }

  // Reorder features within same section
  reorderFeatures(sectionId: string, fromIndex: number, toIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const features = [...updatedSections[sectionIndex].features];
      const [movedFeature] = features.splice(fromIndex, 1);
      features.splice(toIndex, 0, movedFeature);

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features
      };

      this.sectionsSubject.next(updatedSections);
    }
  }
  /**
   * Update features from API response
   * Maps API response structure to internal FeatureSection format
   */
  updateFromApiResponse(apiData: any): void {
    
    if (!apiData) {
      console.warn('⚠️ FeatureDataService: No API data provided');
      return;
    }

    // Normalize the features data structure
    let featuresData: {
      must_have: any[],
      should_have: any[],
      could_have: any[],
      wont_have: any[]
    };

    if (Array.isArray(apiData)) {
      // If it's an array, categorize by moscow_rank
      // Process and categorize features with detailed logging
      const categorizedFeatures = {
        must_have: apiData.filter(f => {
          const rank = f.moscow_rank?.toLowerCase() || '';
          return rank.includes('must') || rank === 'm';
        }),
        should_have: apiData.filter(f => {
          const rank = f.moscow_rank?.toLowerCase() || '';
          return rank.includes('should') || rank === 's';
        }),
        could_have: apiData.filter(f => {
          const rank = f.moscow_rank?.toLowerCase() || '';
          return rank.includes('could') || rank === 'c';
        }),
        wont_have: apiData.filter(f => {
          const rank = f.moscow_rank?.toLowerCase() || '';
          return rank.includes('wont') || rank.includes("won't") || rank === 'w';
        })
      };

      // Features categorized successfully

      featuresData = categorizedFeatures;
    } else if (typeof apiData === 'object') {
      // If it's already categorized or nested in data property
      featuresData = apiData.data?.features || apiData.features || apiData;
    } else {
      console.warn('⚠️ FeatureDataService: Invalid features data format');
      featuresData = {
        must_have: [],
        should_have: [],
        could_have: [],
        wont_have: []
      };
    }


    const sections: FeatureSection[] = [
      {
        id: 'must-have',
        title: 'Mo',
        subtitle: 'MUST HAVE',
        features: this.mapApiFeaturesToCards(featuresData.must_have || [], 'must')
      },
      {
        id: 'should-have',
        title: 'S',
        subtitle: 'SHOULD HAVE',
        features: this.mapApiFeaturesToCards(featuresData.should_have || [], 'should')
      },
      {
        id: 'could-have',
        title: 'Co',
        subtitle: 'COULD HAVE',
        features: this.mapApiFeaturesToCards(featuresData.could_have || [], 'could')
      },
      {
        id: 'wont-have',
        title: 'W',
        subtitle: "WON'T HAVE",
        features: this.mapApiFeaturesToCards(featuresData.wont_have || [], 'wont')
      }
    ];

    // Sections updated successfully

    this.sectionsSubject.next(sections);
  }

  /**
   * Map API feature items to internal FeatureCard format
   */
  private mapApiFeaturesToCards(apiFeatures: any[], prefix: string): FeatureCard[] {

    // Handle case where apiFeatures is not an array
    if (!Array.isArray(apiFeatures)) {
      console.warn(`⚠️ FeatureDataService: ${prefix} features is not an array:`, apiFeatures);
      return [];
    }

    const mappedFeatures = apiFeatures.map((feature, index) => {
      // Normalize feature data structure
      const normalizedFeature = {
        id: feature?.id || `${prefix}-${index + 1}`,
        title: feature?.title || feature?.name || `Feature ${index + 1}`,
        description: feature?.description || feature?.justification || 'No description available',
        tags: this.normalizeFeatureTags(feature?.tags),
        tenant: feature?.tenant || '',
        justification: feature?.justification || '',
        moscow_rank: this.normalizeMoscowRank(feature?.moscow_rank, prefix)
      };

      return normalizedFeature;
    });

    return mappedFeatures;
  }

  private normalizeFeatureTags(tags: any): string[] {
    if (Array.isArray(tags)) {
      return tags.map(tag => String(tag).trim()).filter(tag => tag.length > 0);
    }
    if (typeof tags === 'string') {
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    }
    return [];
  }

  private normalizeMoscowRank(rank: string | undefined, defaultPrefix: string): string {
    if (!rank) return defaultPrefix;
    const normalized = rank.toLowerCase();
    if (normalized.includes('must')) return 'must';
    if (normalized.includes('should')) return 'should';
    if (normalized.includes('could')) return 'could';
    if (normalized.includes('wont') || normalized.includes('won\'t')) return 'wont';
    return defaultPrefix;
  }
}