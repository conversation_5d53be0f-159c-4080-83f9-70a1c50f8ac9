import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { AppStateService } from '../../shared/services/app-state.service';
import { PipelineState, PipelineStep } from '../interfaces/pipeline-api.interface';

/**
 * Interface for project list item response
 */
export interface ProjectListItem {
  id: string;
  run_id: string;
  name: string;
  description: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  status: string;
  tags: string[];
  metadata?: any;
}

/**
 * Interface for project list response
 */
export interface ProjectListResponse {
  projects: ProjectListItem[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Interface for project details request
 */
export interface ProjectDetailsRequest {
  run_id: string;
}

/**
 * Interface for project details response
 */
export interface ProjectDetailsResponse {
  id: string;
  run_id: string;
  name: string;
  description: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  status: string;
  tags: string[];
  metadata?: any;
  market_research: any;
  lean_business_canvas: any;
  user_personas: {
    personas: any[];
  };
  swot_analysis: any;
  features: any;
  roadmap_tasks: any[];
  conversations: any[];
}

/**
 * Service for retrieving recent projects and project details
 */
@Injectable({
  providedIn: 'root'
})
export class GetRecentProjectsService {
  private readonly apiBaseUrl = environment.pipelineApiBaseUrl;

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(
    private http: HttpClient,
    private appStateService: AppStateService
  ) { }

  /**
   * Get list of recent projects
   * @returns Observable with list of projects
   */
  getRecentProjects(): Observable<ProjectListItem[]> {
    const url = `${this.apiBaseUrl}/projects/list`;


    const actualDataFromListProjectAPI = this.http.get<ProjectListResponse>(url, this.httpOptions).pipe(
      map(response => response.projects),
      catchError(this.handleError)
    );

    return actualDataFromListProjectAPI;

  }

  /**
   * Get detailed project data by run_id
   * @param runId - The run_id of the project to retrieve
   * @returns Observable with project details
   */
  getProjectDetailsById(runId: string): Observable<ProjectDetailsResponse> {
    const url = `${this.apiBaseUrl}/project/details`;
    const payload: ProjectDetailsRequest = { run_id: runId };

    return this.http.post<any>(url, payload, this.httpOptions).pipe(
      map(response => {
        // Handle nested project structure from API
        if (response.project) {
          return {
            ...response.project,
            market_research: response.project.market_research || response.market_research,
            lean_business_canvas: response.project.lean_business_canvas || response.lean_business_canvas,
            user_personas: response.project.user_personas || response.user_personas,
            swot_analysis: response.project.swot_analysis || response.swot_analysis,
            features: response.project.features || response.features,
            roadmap_tasks: response.project.roadmap_tasks || response.roadmap_tasks,
            conversations: response.project.conversations || response.conversations || []
          } as ProjectDetailsResponse;
        }
        return response as ProjectDetailsResponse;
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Load project data into app state
   * @param runId - The run_id of the project to load
   * @returns Observable with the loaded project details
   */
  loadProject(runId: string): Observable<ProjectDetailsResponse> {
    return this.getProjectDetailsById(runId).pipe(
      tap(projectDetails => {

        // Transform API response to match internal data structure
        const pipelineState = this.transformProjectDetailsToPipelineState(projectDetails);


        // Update app state with the transformed data
        this.appStateService.updatePipelineState(pipelineState);


        // Verify the state was actually updated
        const currentState = this.appStateService.pipelineState;
      })
    );
  }

  /**
   * Transform project details response to pipeline state format
   * @param details - The project details from API
   * @returns PipelineState object
   */
  private transformProjectDetailsToPipelineState(details: ProjectDetailsResponse): Partial<PipelineState> {

    const transformedState = {
      run_id: details.run_id,
      project_name: details.name,
      project_description: details.description, // Add project description mapping
      industry: details.metadata?.industry || null,
      user_groups: details.metadata?.user_groups || null,
      data: {
        // Map API response fields to internal data structure
        market_research: details.market_research,
        lbc: details.lean_business_canvas,
        persona: {
          personas: details.user_personas?.personas || []
        },
        swot: details.swot_analysis,
        features: details.features,
        roadmap: {
          project_tasks: details.roadmap_tasks || []
        }
      },
      // Mark all steps as completed if data exists
      completed_steps: this.determineCompletedSteps(details),
      // Add timestamp to force observable updates
      lastUpdated: new Date().toISOString()
    };



    return transformedState;
  }

  /**
   * Determine which steps are completed based on project details
   * @param details - The project details from API
   * @returns Array of completed pipeline steps
   */
  private determineCompletedSteps(details: ProjectDetailsResponse): PipelineStep[] {
    const completedSteps: PipelineStep[] = [];

    if (details.market_research) completedSteps.push('market_research' as PipelineStep);
    if (details.lean_business_canvas) completedSteps.push('lbc' as PipelineStep);
    if (details.user_personas?.personas?.length > 0) completedSteps.push('persona' as PipelineStep);
    if (details.swot_analysis) completedSteps.push('swot' as PipelineStep);
    if (details.features) completedSteps.push('features' as PipelineStep);
    if (details.roadmap_tasks?.length > 0) completedSteps.push('roadmap' as PipelineStep);

    return completedSteps;
  }

  /**
   * Error handler for HTTP requests
   * @param error - The HTTP error
   * @returns Observable with error
   */
  private handleError(error: HttpErrorResponse) {
    let errorMessage = '';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server Error: ${error.status} - ${error.message}`;
    }

    console.error('❌ API Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
