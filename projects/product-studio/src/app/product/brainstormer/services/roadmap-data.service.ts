import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { CsvExportService, CsvExportResult } from './export-service/csv-export.service';
import { JiraExportService } from './export-service/jira-export.service';
import { AppStateService } from '../../shared/services/app-state.service';
import { RoadmapData, RoadmapProjectTask, Epic, JiraResponse } from '../interfaces/pipeline-api.interface';

export type Priority = 'low' | 'medium' | 'high';

export interface RoadmapTask {
  id: string;
  task: string; // Used in card view
  name?: string; // Used in timeline view (same as task)
  description: string;
  long_description?: string; // Epic description for CSV export
  priority: Priority;
  startDate: Date;
  endDate: Date;
  quarter: number;
  duration?: number; // Duration in days from API
  color?: string; // Used in timeline view
}

export interface QuarterSection {
  id: string;
  title: string; // 'Quarter 1', 'Quarter 2', etc.
  qurterColor: string;
  subtitle: string; // Date range
  tasks: RoadmapTask[];
}

// For timeline view compatibility
export interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  color: string;
  quarter: number;
}

@Injectable({
  providedIn: 'root'
})
export class RoadmapDataService {
  // Initialize with empty array - data will come from API
  private tasksSubject = new BehaviorSubject<RoadmapTask[]>([]);

  private quartersSubject = new BehaviorSubject<QuarterSection[]>([]);

  // Store original API data for CSV export
  private originalApiDataSubject = new BehaviorSubject<RoadmapData | null>(null);

  constructor(
    private csvExportService: CsvExportService,
    private jiraExportService: JiraExportService,
    private appStateService: AppStateService
  ) {
    this.initializeQuarters();
  }

  // Observable streams
  get tasks$(): Observable<RoadmapTask[]> {
    return this.tasksSubject.asObservable();
  }

  get quarters$(): Observable<QuarterSection[]> {
    return this.quartersSubject.asObservable();
  }

  // Get current values
  get tasks(): RoadmapTask[] {
    return this.tasksSubject.value;
  }

  get quarters(): QuarterSection[] {
    return this.quartersSubject.value;
  }

  // Get original API data for export
  get originalApiData(): RoadmapData | null {
    return this.originalApiDataSubject.value;
  }

  get originalApiData$(): Observable<RoadmapData | null> {
    return this.originalApiDataSubject.asObservable();
  }

  // Convert to GanttTask format for timeline view
  getGanttTasks(): GanttTask[] {
    return this.tasks.map(task => ({
      id: task.id,
      name: task.task,
      startDate: task.startDate,
      endDate: task.endDate,
      color: task.color || this.getDefaultColor(task.priority),
      quarter: task.quarter,
    }));
  }

  // Add new task
  addTask(task: Omit<RoadmapTask, 'id'>): void {
    // Ensure quarter is correctly calculated from start date
    const calculatedQuarter = this.getQuarterFromDate(task.startDate);

    const newTask: RoadmapTask = {
      ...task,
      id: `task-${Date.now()}`,
      quarter: calculatedQuarter, // Override with calculated quarter
      color: task.color || this.getDefaultColor(task.priority),
    };

    const currentTasks = this.tasksSubject.value;
    this.tasksSubject.next([...currentTasks, newTask]);
    this.updateQuarters();
  }

  // Update existing task
  updateTask(taskId: string, updates: Partial<RoadmapTask>): void {
    const currentTasks = this.tasksSubject.value;
    const updatedTasks = currentTasks.map(task => {
      if (task.id === taskId) {
        const updatedTask = { ...task, ...updates };
        // Recalculate quarter if start date is being updated
        if (updates.startDate) {
          updatedTask.quarter = this.getQuarterFromDate(updates.startDate);
        }
        return updatedTask;
      }
      return task;
    });
    this.tasksSubject.next(updatedTasks);
    this.updateQuarters();
  }

  // Delete task
  deleteTask(taskId: string): void {
    const currentTasks = this.tasksSubject.value;
    const filteredTasks = currentTasks.filter(task => task.id !== taskId);
    this.tasksSubject.next(filteredTasks);
    this.updateQuarters();
  }

  // Get task by ID
  getTaskById(taskId: string): RoadmapTask | undefined {
    return this.tasks.find(task => task.id === taskId);
  }

  // Private helper methods
  private initializeQuarters(): void {
    const quarters: QuarterSection[] = [
      {
        id: 'q1',
        title: 'Quarter 1',
        qurterColor: '#68B266',
        subtitle: 'Jan - Mar 2025',
        tasks: [],
      },
      {
        id: 'q2',
        title: 'Quarter 2',
        qurterColor: '#D58D49',
        subtitle: 'Apr - Jun 2025',
        tasks: [],
      },
      {
        id: 'q3',
        title: 'Quarter 3',
        qurterColor: '#8b5cf6',
        subtitle: 'Jul - Sep 2025',
        tasks: [],
      },
      {
        id: 'q4',
        title: 'Quarter 4',
        qurterColor: '#06b6d4',
        subtitle: 'Oct - Dec 2025',
        tasks: [],
      },
    ];

    this.quartersSubject.next(quarters);
    this.updateQuarters();
  }

  private updateQuarters(): void {
    const quarters = this.quartersSubject.value.map(quarter => ({
      ...quarter,
      tasks: this.tasks.filter(task => task.quarter === parseInt(quarter.id.replace('q', ''))),
    }));
    
    this.quartersSubject.next(quarters);
  }

  private getDefaultColor(priority: Priority): string {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  }

  // Utility method to get quarter from date
  getQuarterFromDate(date: Date): number {
    const month = date.getMonth();
    if (month >= 0 && month <= 2) return 1; // Jan-Mar
    if (month >= 3 && month <= 5) return 2; // Apr-Jun
    if (month >= 6 && month <= 8) return 3; // Jul-Sep
    return 4; // Oct-Dec
  }

  /**
   * Export roadmap data to CSV
   * @param filename - Optional custom filename
   * @returns Observable with export result
   */
  exportRoadmapToCsv(filename?: string): Observable<CsvExportResult> {

    // The CsvExportService now handles getting data from pipeline state directly
    return this.csvExportService.exportRoadmapToCsv({
      filename: filename || 'roadmap-epics.csv'
    });
  }

  /**
   * Check if roadmap data is available for export using pipeline state
   * @returns boolean indicating if data is available
   */
  hasDataForExport(): boolean {
    // Try to get data from pipeline state first (most current)
    const pipelineState = this.appStateService?.pipelineState;
    let roadmapData: RoadmapData | null = pipelineState?.data?.roadmap || null;

    // Fallback to original API data if pipeline state not available
    if (!roadmapData) {
      roadmapData = this.originalApiDataSubject.value;
    }

    const hasData = !!(roadmapData && roadmapData.project_tasks && roadmapData.project_tasks.length > 0);
    // Data availability checked

    return hasData;
  }

  // Format date for input fields
  formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Parse date from input fields
  parseDateFromInput(dateString: string): Date {
    return new Date(dateString);
  }

  // Get quarter date range
  getQuarterDateRange(quarter: number, year: number = 2025): { start: Date; end: Date } {
    switch (quarter) {
      case 1:
        return { start: new Date(year, 0, 1), end: new Date(year, 2, 31) }; // Jan-Mar
      case 2:
        return { start: new Date(year, 3, 1), end: new Date(year, 5, 30) }; // Apr-Jun
      case 3:
        return { start: new Date(year, 6, 1), end: new Date(year, 8, 30) }; // Jul-Sep
      case 4:
        return { start: new Date(year, 9, 1), end: new Date(year, 11, 31) }; // Oct-Dec
      default:
        return { start: new Date(year, 0, 1), end: new Date(year, 2, 31) };
    }
  }

  // Validate if date falls within quarter
  isDateInQuarter(date: Date, quarter: number): boolean {
    const calculatedQuarter = this.getQuarterFromDate(date);
    return calculatedQuarter === quarter;
  }

  /**
   * Update roadmap tasks from API response
   * Maps API response structure to internal RoadmapTask format
   */
  updateFromApiResponse(apiData: any): void {

    // Store original API data for CSV export
    const roadmapData: RoadmapData = {
      project_tasks: apiData.project_tasks || []
    };
    this.originalApiDataSubject.next(roadmapData);

    const tasks: RoadmapTask[] = apiData.project_tasks?.map((apiTask: any, index: number) => {
      // Calculate start and end dates based on quarter and duration
      const quarterRange = this.getQuarterDateRange(apiTask.quarter || 1);
      const startDate = quarterRange.start;
      const endDate = new Date(startDate);

      // Add duration days to start date
      if (apiTask.duration) {
        endDate.setDate(startDate.getDate() + apiTask.duration);
      } else {
        // Default to end of quarter if no duration specified
        endDate.setTime(quarterRange.end.getTime());
      }

      return {
        id: `task-${index + 1}`,
        task: apiTask.task || `Task ${index + 1}`,
        name: apiTask.task || `Task ${index + 1}`,
        description: apiTask.description || 'No description available',
        long_description: apiTask.long_description || apiTask.description || 'No description available',
        priority: this.mapApiPriority(apiTask.priority),
        startDate: startDate,
        endDate: endDate,
        quarter: apiTask.quarter || 1,
        duration: apiTask.duration || 0,
        color: this.getDefaultColor(this.mapApiPriority(apiTask.priority))
      };
    }) || [];

    this.tasksSubject.next(tasks);
    this.updateQuarters();
  }

  /**
   * Map API priority strings to internal Priority type
   */
  private mapApiPriority(apiPriority: string): Priority {
    switch (apiPriority?.toLowerCase()) {
      case 'high': return 'high';
      case 'medium': return 'medium';
      case 'low': return 'low';
      default: return 'medium';
    }
  }

  /**
   * Export roadmap data to Jira using pipeline state
   * @returns Observable with Jira export result
   */
  exportRoadmapToJira(): Observable<JiraResponse> {

    // The JiraExportService now handles getting data from pipeline state directly
    return this.jiraExportService.exportRoadmapToJira();
  }

  /**
   * Transform project tasks to Epic format using pipeline state data
   * @returns Array of Epic objects
   */
  getEpicsFromRoadmap(): Epic[] {
    // Try to get data from pipeline state first (most current)
    const pipelineState = this.appStateService?.pipelineState;
    let roadmapData: RoadmapData | null = pipelineState?.data?.roadmap || null;

    // Fallback to original API data if pipeline state not available
    if (!roadmapData) {
      roadmapData = this.originalApiDataSubject.value;
    }

    if (!roadmapData || !roadmapData.project_tasks) {
      return [];
    }

    return this.jiraExportService.transformProjectTasksToEpics(roadmapData.project_tasks);
  }

  /**
   * Get preview of Jira tickets that would be created
   * @returns Array of Epic objects for preview
   */
  getJiraPreview(): Epic[] {
    const apiData = this.originalApiDataSubject.value;

    if (!apiData) {
      return [];
    }

    return this.jiraExportService.getJiraPreview(apiData);
  }
}
