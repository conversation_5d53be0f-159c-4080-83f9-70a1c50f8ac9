import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ChangeDetectorRef } from '@angular/core';
import { catchError, EMPTY, finalize } from 'rxjs';
import { CommonModule } from '@angular/common';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';

import {
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent as AweSplitScreenComponent
} from '@awe/play-comp-library';
import {
  StepperService,
  StepperStep,
} from '../../shared/services/stepper-service/stepper.service';

// Import all page components

import { UserPersonaComponent } from '../pages/user-persona/user-persona.component';
import { PersonaDetailsComponent } from '../pages/persona-details/persona-details.component';
import { FeatureListComponent } from '../pages/feature-list/feature-list.component';
import { SwotAnalysisComponent } from '../pages/swot-analysis/swot-analysis.component';
import { ProductRoadmapComponent } from '../pages/product-roadmap/product-roadmap.component';
// Remove the custom split-screen component import as we're using AVA play library
import { ChatPanelComponent } from '../components/chat-panel/chat-panel.component';
import { LeftPanelHeaderComponent } from '../components/left-panel-header/left-panel-header.component';
import { NewRightPanelHeaderComponent } from '../components/new-right-panel-header/new-right-panel-header.component';
import { UnderstandingComponent } from '../pages/understanding/understanding.component';
import { Router } from '@angular/router';
import { ProductPipelineService } from '../services/product-pipeline.service';
// LoadingComponent removed - loading states now handled by stepper
import { AppStateService } from '../../shared/services/app-state.service';
import { NavigationGuardService } from '../../shared/services/navigation-guard.service';
import { NavigationConfirmationModalComponent } from '../../shared/components/navigation-confirmation-modal/navigation-confirmation-modal.component';
import { PanelToggleService } from '../services/panel-toggle.service';
import { ThemeServiceService } from '../../shared/services/auth-config-service/theme-service.service';
import { SummaryService } from '../components/summary/summary.service';
import { SummaryComponent } from '../components/summary/summary.component';
// import { SummaryComponent } from '../components/summary/summaryO.component';
// import { DebugStateComponent } from '../components/debug-state/debug-state.component';

@Component({
  selector: 'app-brainstorming',
  imports: [
    CommonModule,
    UnderstandingComponent,
    UserPersonaComponent,
    PersonaDetailsComponent,
    FeatureListComponent,
    SwotAnalysisComponent,
    ProductRoadmapComponent,
    AweSplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    ChatPanelComponent,
    LeftPanelHeaderComponent,
    NewRightPanelHeaderComponent,
    NavigationConfirmationModalComponent,
    SummaryComponent,
    // DebugStateComponent,
  ],
  templateUrl: './brainstorming.component.html',
  styleUrl: './brainstorming.component.scss',
  animations: [
    trigger('roboBallAnimation', [
      state(
        'idle',
        style({
          transform: 'scale(1) rotate(0deg)',
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.15)',
        }),
      ),
      state(
        'hover',
        style({
          transform: 'scale(1.1) rotate(5deg)',
          boxShadow: '0px 8px 25px rgba(74, 144, 226, 0.3)',
        }),
      ),
      state(
        'clicked',
        style({
          transform: 'scale(0.95) rotate(-5deg)',
          boxShadow: '0px 4px 15px rgba(74, 144, 226, 0.5)',
        }),
      ),
      transition(
        'idle => hover',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition(
        'hover => idle',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('* => clicked', animate('0.15s ease-in')),
      transition('clicked => *', animate('0.3s ease-out')),
    ]),
    trigger('promptAnimation', [
      state(
        'hidden',
        style({
          opacity: 0,
          transform: 'translateX(-20px) scale(0.9)',
        }),
      ),
      state(
        'visible',
        style({
          opacity: 1,
          transform: 'translateX(0) scale(1)',
        }),
      ),
      transition(
        'hidden => visible',
        animate('0.4s 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('visible => hidden', animate('0.2s ease-in')),
    ]),
  ],
})
export class BrainstormingComponent implements OnInit, OnDestroy {
  roboBallIcon: string = ' icons/robo_ball.svg';
  chevronLeftIcon: string = 'assets/icons/awe_chevron_left.svg';
  chevronRightIcon: string = 'assets/icons/awe_chevron_right.svg';
  // State observables
  currentStep$: Observable<StepperStep | null>;
  currentStepIndex$: Observable<number>;
  canGoNext$: Observable<boolean>;
  canGoPrevious$: Observable<boolean>;
  isLoading$: Observable<boolean>;
  loadingMessage$: Observable<string | null>;
  hasErrors$: Observable<boolean>;
  showSplitScreen$: Observable<boolean>;

  // Navigation confirmation modal
  showNavigationModal$: Observable<boolean>;

  // Local state
  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;

  // Summary display state
  showSummary: boolean = false;

  // Finish button loading state
  isFinishLoading: boolean = false;

  // Header navigation loading state
  isHeaderLoading: boolean = false;
  headerLoadingMessage: string = '';

  // Loading states are now handled by the stepper component

  // State for left panel toggle - using AVA play library directly
  leftPanelWidth: number = 25; // Default width for left panel
  isDarkMode = false;

  // Track if any edit modal is open across all components
  isAnyEditModalOpen = false;

  // Callbacks removed - using AVA play library directly

  // Animation states
  roboBallState = 'idle';
  promptState = 'visible';

  // Persona details state
  showPersonaDetails = false;
  selectedPersonaId: string | null = null;

  // Error states (loading now handled by stepper)
  stepError: string | null = null;

  // Theme observable
  currentTheme$: Observable<'light' | 'dark'>;

  private subscriptions: Subscription[] = [];

  constructor(
    public stepperService: StepperService, // Made public for template access
    private router: Router,
    private pipelineService: ProductPipelineService,
    private appStateService: AppStateService,
    @Inject(NavigationGuardService) private navigationGuardService: NavigationGuardService,
    public panelToggleService: PanelToggleService, // Made public for template access
    private cdr: ChangeDetectorRef,
    private themeService: ThemeServiceService,
    private summaryService: SummaryService,
   
  ) {
    // Initialize state observables
    this.currentStep$ = this.stepperService.currentStep$;
    this.currentStepIndex$ = this.stepperService.currentStepIndex$;
    this.canGoNext$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoNext)
    );
    this.canGoPrevious$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoPrevious)
    );
    this.isLoading$ = this.appStateService.selectIsLoading();
    this.currentTheme$ = this.themeService.themeObservable;
    this.loadingMessage$ = this.appStateService.loadingState$.pipe(
      map(state => state.loadingMessage)
    );
    this.hasErrors$ = this.appStateService.selectHasErrors();
    this.showSplitScreen$ = this.appStateService.uiState$.pipe(
      map(state => state.showSplitScreen)
    );

    // Navigation confirmation modal
    this.showNavigationModal$ = this.navigationGuardService.showConfirmationModal$;
  }

  ngOnInit(): void {

    // Check current app state when component initializes
    const currentPipelineState = this.appStateService.pipelineState;

    // Initialize from stored data if available
    this.initializeFromStoredData();

    // Subscribe to current step changes
    this.subscriptions.push(
      this.stepperService.currentStep$.subscribe((step) => {
        this.currentStep = step;
      }),
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe((index) => {
        this.currentStepIndex = index;
      }),
    );

    // Subscribe to pipeline state changes to handle API responses
    this.subscriptions.push(
      this.appStateService.pipelineState$.subscribe((pipelineState) => {

        // When market research API completes, update stepper to show Understanding step
        if (pipelineState.current_step === 'market_research' &&
          pipelineState.data &&
          Object.keys(pipelineState.data).length > 0) {
          this.stepperService.updateStepFromApiResponse('market_research');
        }
      })
    );

    // Loading state blocking removed to allow continuous step progression
  }



  finish(): void {

    // Prevent multiple clicks during loading
    if (this.isFinishLoading) {
      return;
    }

    // Start loading state
    this.isFinishLoading = true;

    // Subscribe to the summary data refresh
    const finishSub = this.summaryService.forceDataRefresh().pipe(
        catchError(error => {
          console.error('Error refreshing summary data:', error);
          this.isFinishLoading = false;
          return EMPTY;
        }),
        finalize(() => {
          this.isFinishLoading = false;
          this.cdr.detectChanges();
        })
      )
      .subscribe({
        next: (summaryData: Record<string, unknown>) => {
          this.showSummary = true;
          this.cdr.detectChanges();
        },
        complete: () => {
        }
      });
      
    this.subscriptions.push(finishSub);

    // Get current state and prepare for summary view
    const currentState = this.appStateService.pipelineState;


    // Hide split screen immediately
    // this.appStateService.updateUIState({ showSplitScreen: false });

    // Force state update with latest data
    this.appStateService.updatePipelineState({
      ...currentState,
      lastUpdated: new Date().toISOString()
    });

    // Verification of data completed

    // Force summary service to refresh data and update session storage
    this.summaryService.forceDataRefresh();

    // Simulate loading time (3 seconds) before showing summary
    // setTimeout(() => {
      // Show summary view and hide split screen
      
      // this.isFinishLoading = false;
    // }, 3000);
  }





  /**
   * Initialize stepper state from current BehaviorSubject state
   */
  private initializeFromStoredData(): void {
      if(this.appStateService.hasStoredData()) {
      const currentState = this.appStateService.pipelineState;

      // Restore stepper state from current data
      this.stepperService.restoreFromStoredData({
        ...currentState.data,
        current_step: currentState.current_step
      });

    }
  }

  /**
   * Clear stored data and reset stepper (useful for starting new project)
   */
  clearStoredDataAndReset(): void {
    this.appStateService.resetState();
  }

  /**
   * Handle navigation confirmation modal cancel
   */
  onNavigationCancel(): void {
    this.navigationGuardService.cancelNavigation();
  }

  /**
   * Handle navigation confirmation modal confirm
   */
  onNavigationConfirm(): void {
    this.navigationGuardService.confirmNavigation();
  }
  ngOnDestroy(): void {
    // Clean up any active subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());

  }

  onStepChanged(event: { step: StepperStep; index: number }): void {
    // Handle step change if needed
  }

  // Loading messages are now handled by the stepper component

  nextStep(): void {
    // Allow continuous step progression - blocking removed
    if (this.stepperService.canGoNext()) {
      const nextStepIndex = this.currentStepIndex + 1;
      const nextStep = this.stepperService.getStepByIndex(nextStepIndex);

      if (nextStep) {
        // Clear previous errors
        this.stepError = null;

        // Check if the next step already has data (smart navigation)
        const shouldCallApi = this.stepperService.shouldCallApiForStep(nextStep.id);

        if (shouldCallApi) {
          // Get the API step that should be called for the current stepper step
          const pipelineCurrentStep = this.stepperService.getNextApiStepForCurrentStep();

          if (pipelineCurrentStep) {
            // Start header loading state
            this.isHeaderLoading = true;
            this.headerLoadingMessage = `Loading ${nextStep.label}...`;

            // Call pipeline API for the next step
            this.pipelineService.progressToStep(pipelineCurrentStep).subscribe({
              next: (response) => {

                // Update stepper based on API response
                this.stepperService.updateStepFromApiResponse(response.step as any);

                // End header loading state
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
              error: (error) => {
                console.error(
                  `❌ Error progressing to step ${pipelineCurrentStep}:`,
                  error,
                );
                this.stepError = `Failed to progress to ${nextStep.label}. Please try again.`;

                // End header loading state on error
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
            });
          } else {
            // For steps that don't require API calls, just move forward
            this.stepperService.nextStep();
            this.stepperService.markStepAsCompleted(this.currentStepIndex - 1);
          }
        } else {
          // Step already has data, just navigate without API call
          this.stepperService.nextStep();
        }
      }
    }
  }

  previousStep(): void {
    this.stepperService.previousStep();
  }

  // Navigation state now handled directly by stepper service in template

  toggleLeftPanel(): void {
    // Implement left panel toggle logic
  }

  // Helper method to get current component name for rendering
  getCurrentComponent(): string {
    return this.currentStep?.component || 'understanding';
  }

  // Helper method to check if a specific component should be shown
  shouldShowComponent(componentName: string): boolean {
    return this.getCurrentComponent() === componentName;
  }

  // Split screen functionality is now handled by AVA play library directly
  // No need for service methods as the component is always visible

  /**
   * Clear step error message
   */
  clearStepError(): void {
    this.stepError = null;
  }

  /**
   * Handle split screen close event
   */
  // onSplitScreenClose(): void {
  //   this.roboBallState = 'idle';
  // }

  /**
   * Handle split screen open event
   */
  // onSplitScreenOpen(): void {
  //   this.roboBallState = 'idle';
  // }

  onRoboBallHover() {
    if (this.roboBallState === 'idle') {
      this.roboBallState = 'hover';
    }
  }

  onRoboBallLeave() {
    if (this.roboBallState === 'hover') {
      this.roboBallState = 'idle';
    }
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
  }

  // --- Persona Details Methods ---
  showPersonaDetailsView(personaId: string): void {
    this.selectedPersonaId = personaId;
    this.showPersonaDetails = true;
  }

  hidePersonaDetailsView(): void {
    this.showPersonaDetails = false;
    this.selectedPersonaId = null;
  }

  // Helper method to check if persona details should be shown
  shouldShowPersonaDetails(): boolean {
    return this.shouldShowComponent('persona') && this.showPersonaDetails;
  }

  // Helper method to check if persona list should be shown
  shouldShowPersonaList(): boolean {
    return this.shouldShowComponent('persona') && !this.showPersonaDetails;
  }
}
