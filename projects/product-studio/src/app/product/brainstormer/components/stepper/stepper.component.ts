import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-brainstormer-stepper',
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class BrainstormerStepperComponent implements OnInit, OnDestroy {
  @Input() showDescriptions: boolean = false;
  @Input() showProgressBar: boolean = true;
  @Input() allowStepClick: boolean = true;
  @Output() stepChanged = new EventEmitter<{ step: StepperStep; index: number }>();

  steps: StepperStep[] = [];
  currentStepIndex: number = 0;
  progressPercentage: number = 0;

  private subscriptions: Subscription[] = [];

  constructor(private stepperService: StepperService) {}

  ngOnInit(): void {
    // Subscribe to steps changes
    this.subscriptions.push(
      this.stepperService.steps$.subscribe(steps => {
        this.steps = steps;
        this.updateProgress();
      })
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe(index => {
        this.currentStepIndex = index;
        this.updateProgress();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onStepClick(index: number): void {
    if (!this.allowStepClick) return;

    const targetStep = this.steps[index];

    // Allow navigation to completed steps even during API loading
    // Allow navigation to active step
    // Only prevent navigation to inactive steps
    const canNavigate = targetStep.state === 'completed' ||
                       targetStep.state === 'active';

    if (canNavigate) {
      this.stepperService.goToStep(index);
      this.stepChanged.emit({
        step: this.steps[index],
        index: index
      });
    } else {
    }
  }

  private updateProgress(): void {
    this.progressPercentage = this.stepperService.getProgress();
  }

  // Utility methods for template
  isStepClickable(index: number): boolean {
    if (!this.allowStepClick) return false;

    const step = this.steps[index];
    // Allow clicking on completed steps and the current active step
    // Allow navigation to completed steps even during API loading
    // Only disable clicking on inactive steps
    return step.state === 'completed' || step.state === 'active' || step.state === 'loading';
  }

  getStepClass(step: StepperStep, index: number): string {
    const classes: string[] = [step.state];

    if (this.isStepClickable(index)) {
      classes.push('clickable');
    }

    return classes.join(' ');
  }
}