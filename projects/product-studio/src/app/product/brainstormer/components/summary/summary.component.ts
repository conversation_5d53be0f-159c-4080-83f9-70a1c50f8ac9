import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetector<PERSON>ef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';

import { HeadingComponent, CaptionComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../awe-card/awe-card.component';
import { AweProgressBarComponent } from "../awe-progress-bar/awe-progress-bar.component";
import { CustomFeatureListComponent, CustomFeature } from '../custom-feature-list/custom-feature-list.component';
import { SummaryService } from './summary.service';
import { SummaryData } from './summary.interfaces';
import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
import { AppStateService } from '../../../shared/services/app-state.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';
import { Epic } from '../../interfaces/pipeline-api.interface';
import { ProductPipelineService } from '../../services/product-pipeline.service';

@Component({
  selector: 'app-summary',
  standalone: true,
  imports: [
    CommonModule,
    AweCardComponent,
    IconsComponent,
    HeadingComponent,

    AweProgressBarComponent,
    CaptionComponent,
    CustomFeatureListComponent
  ],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class SummaryComponent implements OnInit, OnDestroy, AfterViewInit {
  // Observables
  summaryData$: Observable<SummaryData>;
  projectName$: Observable<string>;

  // Component state
  summaryData: SummaryData | null = null;
  currentStep: StepperStep | null = null;
  isEditingTitle = false;
  private originalTitle = '';
  private subscriptions: Subscription[] = [];

  constructor(
    private summaryService: SummaryService,
    private stepperService: StepperService,
    private appStateService: AppStateService,
    private productPipelineService: ProductPipelineService,
    private roadmapDataService: RoadmapDataService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {

    
    // Initialize observables
    this.summaryData$ = this.summaryService.summaryData$;
    this.projectName$ = this.summaryService.getProjectName$();
    
    // Initial subscription to ensure immediate data updates
    this.setupDataSubscription();
    
    // Load initial data
    this.loadInitialData();
  }

  // Lifecycle hooks
  ngOnInit(): void {

    
    // Subscribe to stepper service
    this.subscribeToStepperService();

    // Force immediate data refresh
    this.summaryService.forceDataRefresh();
  }

  ngAfterViewInit(): void {    

    
    // Ensure data is displayed after view is initialized
    setTimeout(() => {
      if (!this.summaryData) {

        this.summaryService.forceDataRefresh();
      }
      this.cdr.detectChanges();
    }, 0);
  }
  
  ngOnDestroy(): void {

    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Private setup methods
  private setupDataSubscription(): void {

    
    this.subscriptions.push(
      this.summaryData$.subscribe({
        next: (data) => {
          if (data) {

            this.summaryData = data;
            this.cdr.detectChanges();
          }
        },
        error: (error) => console.error('❌ Error in summary data subscription:', error)
      })
    );
  }

  private loadInitialData(): void {


    // Try loading from session storage first
    const storedData = this.summaryService.getSummaryDataFromStorage();
    
    if (storedData?.hasData) {

      this.summaryData = storedData;
    }

    // Subscribe to pipeline state changes
    const pipelineSubscription = this.appStateService.pipelineState$.subscribe(pipelineState => {

      if (pipelineState) {
        this.summaryService.forceDataRefresh();
      }
    });

    this.subscriptions.push(pipelineSubscription);
  }

  private subscribeToStepperService(): void {

    
    const stepperSub = this.stepperService.currentStep$.subscribe({
      next: (step) => {

        this.currentStep = step;
        this.cdr.detectChanges();
      },
      error: (error) => console.error('❌ Error in stepper subscription:', error)
    });
    
    this.subscriptions.push(stepperSub);
  }

  // Public methods
  public forceRefreshWithChangeDetection(): void {


    // Force service to refresh data
    this.summaryService.forceDataRefresh();

    // Get current data immediately
    this.summaryData = this.summaryService.getCurrentSummaryData();

    // Force change detection to update UI
    this.cdr.detectChanges();


  }

  // Export methods
  exportToExperienceStudio(): void {

  }

  exportToCsv(): void {

    if (!this.roadmapDataService.hasDataForExport()) {
      console.warn('⚠️ No roadmap data available for CSV export');
      alert('No roadmap data available to export. Please complete the roadmap step first.');
      return;
    }


    this.roadmapDataService.exportRoadmapToCsv().subscribe({
      next: (result) => {
        const message = `Successfully exported ${result.rowCount} roadmap epics to "${result.filename}"`;
        alert(message);
      },
      error: (error) => {
        console.error('❌ CSV export failed:', error);
        alert('Failed to export CSV. Please try again.');
        console.error('❌ CSV export error details:', error);
      }
    });
  }

  exportToJira(): void {

    // Always allow export as per requirements, but check if we have data to export
    const epics = this.roadmapDataService.getEpicsFromRoadmap();

    if (epics.length === 0) {
      console.warn('⚠️ No roadmap epics available for Jira export');
      alert('No roadmap epics available to export. Please complete the roadmap step first.');
      return;
    }

    // Show confirmation dialog
    const confirmMessage = `This will create ${epics.length} Jira tickets from your roadmap epics. Continue?`;
    if (!confirm(confirmMessage)) {
      return;
    }


    // Export to Jira using pipeline state data
    this.roadmapDataService.exportRoadmapToJira().subscribe({
      next: (result) => {
        if (result.success) {
          const message = `Successfully created ${result.ticketsCreated || epics.length} Jira tickets!`;
          alert(message);
        } else {
          const message = `Jira export completed with message: ${result.message}`;
          alert(message);
        }
      },
      error: (error) => {
        console.error('❌ Jira export failed:', error);
        const errorMessage = error.error || error.message || 'Failed to export to Jira. Please try again.';
        alert(`Export failed: ${errorMessage}`);
        console.error('❌ Jira export error details:', error);
      }
    });
  }

  exportProject(): void {
    if (this.summaryData && this.summaryData.hasData) {
      alert('Export functionality will be implemented soon!');
    } else {
      console.warn('⚠️ No data available to export');
      alert('Please complete some brainstorming steps before exporting.');
    }
  }

  // Getters and checks
  getFormattedTitle(): string {
    return this.summaryData?.name || 'Untitled Project';
  }

  hasStepData(step: string): boolean {
    return this.summaryService.hasStepData(step);
  }

  getProjectName(): string {
    const projectName = this.productPipelineService.getProjectName();
    return projectName || '';
  }

  isCsvExportAvailable(): boolean {
    return true;
  }

  isJiraExportAvailable(): boolean {
    return true;
  }

  isExperienceStudioExportAvailable(): boolean {
    return false;
  }

  getJiraEpicsPreview(): Epic[] {
    return this.roadmapDataService.getEpicsFromRoadmap();
  }

  refreshFromSessionStorage(): void {
    const storedData = this.summaryService.getSummaryDataFromStorage();
    if (storedData) {
      this.summaryData = storedData;
    }
  }

  // Project title editing
  startEditingTitle(): void {
    this.originalTitle = this.getProjectName();
    this.isEditingTitle = true;

    setTimeout(() => {
      const input = document.querySelector('.project-title-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 0);
  }

  saveTitle(newTitle: string): void {
    const trimmedTitle = newTitle?.trim();

    if (trimmedTitle && trimmedTitle !== this.originalTitle) {
      this.setProjectName(trimmedTitle);
    }

    this.isEditingTitle = false;
    this.originalTitle = '';
  }

  setProjectName(projectName: string): void {
    if (projectName && projectName.trim()) {
      this.productPipelineService.setProjectName(projectName.trim());
    }
  }

  cancelEditingTitle(): void {
    this.isEditingTitle = false;
    this.originalTitle = '';
  }

  // Navigation methods
  redirectToUnderstanding(): void {
    this.navigateToBrainstormingStep('understanding');
  }

  redirectToPersona(): void {
    this.navigateToBrainstormingStep('persona');
  }

  redirectToFeature(): void {
    this.navigateToBrainstormingStep('features');
  }

  redirectToSwot(): void {
    this.navigateToBrainstormingStep('swot');
  }

  redirectToRoadmap(): void {
    this.navigateToBrainstormingStep('roadmap');
  }

  private navigateToBrainstormingStep(stepId: string): void {
    // First navigate to the brainstorming route if not already there
    const currentUrl = this.router.url;

    if (!currentUrl.includes('/brainstorming')) {
      this.router.navigate(['/product/brainstormer/brainstorming']).then(() => {
        this.stepperService.goToStepById(stepId);
      });
    } else {
      this.stepperService.goToStepById(stepId);
    }
  }

  // Data transformation
  getCustomFeatures(): CustomFeature[] {
    if (!this.summaryData?.features) {
      return [];
    }
    return this.summaryData.features.map(feature => ({
      name: feature.name,
      color: feature.color,
      category: feature.category
    }));
  }

  // UI helpers
  getPlaceholderText(step: string): string {
    switch (step) {
      case 'understanding':
        return 'Complete the Understanding step to see market insights here.';
      case 'personas':
        return 'Complete the User Persona step to see personas here.';
      case 'features':
        return 'Complete the Feature List step to see features here.';
      case 'swot':
        return 'Complete the SWOT Analysis step to see analysis here.';
      case 'roadmap':
        return 'Complete the Roadmap step to see timeline here.';
      default:
        return 'No data available yet.';
    }
  }
}
