// import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef, AfterViewInit } from '@angular/core';
// import { CommonModule } from '@angular/common';
// import { Router } from '@angular/router';
// import { Observable, Subscription } from 'rxjs';
// import { take } from 'rxjs/operators';
// import { HeadingComponent, BodyTextComponent, CaptionComponent } from '@awe/play-comp-library';
// import { IconsComponent } from '../../../../shared/components/icons/icons.component';
// import { AweCardComponent } from '../awe-card/awe-card.component';
// import { AweProgressBarComponent } from "../awe-progress-bar/awe-progress-bar.component";
// import { CustomFeatureListComponent, CustomFeature } from '../custom-feature-list/custom-feature-list.component';
// import { SummaryService } from './summary.service';
// import { SummaryData } from './summary.interfaces';
// import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
// import { AppStateService } from '../../../shared/services/app-state.service';
// import { RoadmapDataService } from '../../services/roadmap-data.service';
// import { Epic } from '../../interfaces/pipeline-api.interface';
// import { ProductPipelineService } from '../../services/product-pipeline.service';

// @Component({
//   selector: 'app-summary',
//   standalone: true,
//   imports: [
//     CommonModule,
//     AweCardComponent,
//     IconsComponent,
//     HeadingComponent,
//     BodyTextComponent,
//     AweProgressBarComponent,
//     CaptionComponent,
//     CustomFeatureListComponent
//   ],
//   templateUrl: './summary.component.html',
//   styleUrl: './summary.component.scss'
// })
// export class SummaryComponent implements OnInit, OnDestroy, AfterViewInit {
//   // Observables
//   summaryData$: Observable<SummaryData>;
//   projectName$: Observable<string>;

//   // Component state
//   summaryData: SummaryData | null = null;
//   currentStep: StepperStep | null = null;
//   isEditingTitle = false;
//   private originalTitle = '';
//   private subscriptions: Subscription[] = [];

//   constructor(
//     private summaryService: SummaryService,
//     private stepperService: StepperService,
//     private appStateService: AppStateService,
//     private productPipelineService: ProductPipelineService,
//     private roadmapDataService: RoadmapDataService,
//     private router: Router,
//     private cdr: ChangeDetectorRef
//   ) {
//     console.log('🏗️ SummaryComponent: Initializing');
    
//     // Initialize observables
//     this.summaryData$ = this.summaryService.summaryData$;
//     this.projectName$ = this.summaryService.getProjectName$();
    
//     // Initial subscription to ensure immediate data updates
//     this.setupDataSubscription();
    
//     // Load initial data
//     this.loadInitialData();
//   }

//   // Lifecycle hooks
//   ngOnInit(): void {
//     console.log('🏁 SummaryComponent: Initializing');
    
//     // Subscribe to stepper service
//     this.subscribeToStepperService();

//     // Force immediate data refresh
//     this.summaryService.forceDataRefresh();
//   }

//   ngAfterViewInit(): void {    
//     console.log('🎨 SummaryComponent: View initialized');
    
//     // Ensure data is displayed after view is initialized
//     setTimeout(() => {
//       if (!this.summaryData) {
//         console.log('🔄 No data after view init, forcing refresh');
//         this.summaryService.forceDataRefresh();
//       }
//       this.cdr.detectChanges();
//     }, 0);
//   }
  
//   ngOnDestroy(): void {
//     console.log('🧹 SummaryComponent destroyed, cleaning up subscriptions');
//     this.subscriptions.forEach(sub => sub.unsubscribe());
//   }

//   // Private setup methods
//   private setupDataSubscription(): void {
//     console.log('🔄 Setting up summary data subscription');
    
//     this.subscriptions.push(
//       this.summaryData$.subscribe({
//         next: (data) => {
//           if (data) {
//             console.log('✅ Received summary data update:', data);
//             this.summaryData = data;
//             this.cdr.detectChanges();
//           }
//         },
//         error: (error) => console.error('❌ Error in summary data subscription:', error)
//       })
//     );
//   }

//   private loadInitialData(): void {
//     console.log('🔄 Loading initial data');

//     // Try loading from session storage first
//     const storedData = this.summaryService.getSummaryDataFromStorage();
    
//     if (storedData?.hasData) {
//       console.log('✅ Loaded data from session storage:', storedData);
//       this.summaryData = storedData;
//     }

//     // Subscribe to pipeline state changes
//     const pipelineSubscription = this.appStateService.pipelineState$.subscribe(pipelineState => {
//       console.log('🔄 Pipeline state changed:', pipelineState);
//       if (pipelineState) {
//         this.summaryService.forceDataRefresh();
//       }
//     });

//     this.subscriptions.push(pipelineSubscription);
//   }

//   private subscribeToStepperService(): void {
//     console.log('🔄 Setting up stepper subscription');
    
//     const stepperSub = this.stepperService.currentStep$.subscribe({
//       next: (step) => {
//         console.log('📍 Current step updated:', step);
//         this.currentStep = step;
//         this.cdr.detectChanges();
//       },
//       error: (error) => console.error('❌ Error in stepper subscription:', error)
//     });
    
//     this.subscriptions.push(stepperSub);
//   }

//   // Public methods
//   public forceRefreshWithChangeDetection(): void {
//     console.log('🔄 Force refreshing summary data with change detection');

//     // Force service to refresh data
//     this.summaryService.forceDataRefresh();

//     // Get current data immediately
//     this.summaryData = this.summaryService.getCurrentSummaryData();

//     // Force change detection to update UI
//     this.cdr.detectChanges();

//     console.log('✅ Force refresh with change detection completed', this.summaryData);
//   }

//   // Export methods
//   exportToExperienceStudio(): void {
//     console.log('🔄 SummaryComponent: Exporting to Experience Studio');
//   }

//   exportToCsv(): void {
//     console.log('🔄 SummaryComponent: Exporting roadmap to CSV');

//     if (!this.roadmapDataService.hasDataForExport()) {
//       console.warn('⚠️ No roadmap data available for CSV export');
//       alert('No roadmap data available to export. Please complete the roadmap step first.');
//       return;
//     }

//     console.log('📤 Starting CSV export using pipeline state data');

//     this.roadmapDataService.exportRoadmapToCsv().subscribe({
//       next: (result) => {
//         console.log('✅ CSV export successful:', result);
//         const message = `Successfully exported ${result.rowCount} roadmap epics to "${result.filename}"`;
//         alert(message);
//         console.log('✅ CSV export completed successfully');
//       },
//       error: (error) => {
//         console.error('❌ CSV export failed:', error);
//         alert('Failed to export CSV. Please try again.');
//         console.error('❌ CSV export error details:', error);
//       }
//     });
//   }

//   exportToJira(): void {
//     console.log('🔄 SummaryComponent: Exporting roadmap to Jira');

//     // Always allow export as per requirements, but check if we have data to export
//     const epics = this.roadmapDataService.getEpicsFromRoadmap();
//     console.log('📋 Epics to be created in Jira:', epics);

//     if (epics.length === 0) {
//       console.warn('⚠️ No roadmap epics available for Jira export');
//       alert('No roadmap epics available to export. Please complete the roadmap step first.');
//       return;
//     }

//     // Show confirmation dialog
//     const confirmMessage = `This will create ${epics.length} Jira tickets from your roadmap epics. Continue?`;
//     if (!confirm(confirmMessage)) {
//       console.log('❌ User cancelled Jira export');
//       return;
//     }

//     console.log('📤 Starting Jira export to: https://avaplus-prod.avateam.io/alm/issue');
//     console.log('📊 Using pipeline state data for export');

//     // Export to Jira using pipeline state data
//     this.roadmapDataService.exportRoadmapToJira().subscribe({
//       next: (result) => {
//         console.log('✅ Jira export successful:', result);
//         if (result.success) {
//           const message = `Successfully created ${result.ticketsCreated || epics.length} Jira tickets!`;
//           alert(message);
//           console.log('✅ Jira export completed successfully');
//         } else {
//           const message = `Jira export completed with message: ${result.message}`;
//           alert(message);
//           console.log('⚠️ Jira export completed with warnings');
//         }
//       },
//       error: (error) => {
//         console.error('❌ Jira export failed:', error);
//         const errorMessage = error.error || error.message || 'Failed to export to Jira. Please try again.';
//         alert(`Export failed: ${errorMessage}`);
//         console.error('❌ Jira export error details:', error);
//       }
//     });
//   }

//   exportProject(): void {
//     if (this.summaryData && this.summaryData.hasData) {
//       console.log('📤 Exporting project data:', this.summaryData);
//       alert('Export functionality will be implemented soon!');
//     } else {
//       console.warn('⚠️ No data available to export');
//       alert('Please complete some brainstorming steps before exporting.');
//     }
//   }

//   // Getters and checks
//   getFormattedTitle(): string {
//     return this.summaryData?.name || 'Untitled Project';
//   }

//   hasStepData(step: string): boolean {
//     return this.summaryService.hasStepData(step);
//   }

//   getProjectName(): string {
//     const projectName = this.productPipelineService.getProjectName();
//     console.log('📋 SummaryComponent: Getting project name:', projectName);
//     return projectName || '';
//   }

//   isCsvExportAvailable(): boolean {
//     console.log('📊 CSV export availability check - always enabled');
//     return true;
//   }

//   isJiraExportAvailable(): boolean {
//     console.log('📊 Jira export availability check - always enabled');
//     return true;
//   }

//   isExperienceStudioExportAvailable(): boolean {
//     console.log('📊 Experience Studio export availability check - always disabled');
//     return false;
//   }

//   getJiraEpicsPreview(): Epic[] {
//     return this.roadmapDataService.getEpicsFromRoadmap();
//   }

//   refreshFromSessionStorage(): void {
//     const storedData = this.summaryService.getSummaryDataFromStorage();
//     if (storedData) {
//       console.log('🔄 Refreshing summary data from session storage');
//       this.summaryData = storedData;
//     }
//   }

//   // Project title editing
//   startEditingTitle(): void {
//     this.originalTitle = this.getProjectName();
//     this.isEditingTitle = true;
//     console.log('✏️ Started editing project title');

//     setTimeout(() => {
//       const input = document.querySelector('.project-title-input') as HTMLInputElement;
//       if (input) {
//         input.focus();
//         input.select();
//       }
//     }, 0);
//   }

//   saveTitle(newTitle: string): void {
//     const trimmedTitle = newTitle?.trim();

//     if (trimmedTitle && trimmedTitle !== this.originalTitle) {
//       this.setProjectName(trimmedTitle);
//       console.log('💾 Saved project title:', trimmedTitle);
//     }

//     this.isEditingTitle = false;
//     this.originalTitle = '';
//   }

//   setProjectName(projectName: string): void {
//     if (projectName && projectName.trim()) {
//       this.productPipelineService.setProjectName(projectName.trim());
//       console.log('📝 Project name updated:', projectName);
//     }
//   }

//   cancelEditingTitle(): void {
//     this.isEditingTitle = false;
//     this.originalTitle = '';
//     console.log('❌ Cancelled editing project title');
//   }

//   // Navigation methods
//   redirectToUnderstanding(): void {
//     console.log('🧭 Navigating to Understanding step');
//     this.navigateToBrainstormingStep('understanding');
//   }

//   redirectToPersona(): void {
//     console.log('🧭 Navigating to User Persona step');
//     this.navigateToBrainstormingStep('persona');
//   }

//   redirectToFeature(): void {
//     console.log('🧭 Navigating to Feature List step');
//     this.navigateToBrainstormingStep('features');
//   }

//   redirectToSwot(): void {
//     console.log('🧭 Navigating to SWOT Analysis step');
//     this.navigateToBrainstormingStep('swot');
//   }

//   redirectToRoadmap(): void {
//     console.log('🧭 Navigating to Roadmap step');
//     this.navigateToBrainstormingStep('roadmap');
//   }

//   private navigateToBrainstormingStep(stepId: string): void {
//     // First navigate to the brainstorming route if not already there
//     const currentUrl = this.router.url;
//     console.log('🧭 Current URL:', currentUrl);
//     console.log('🧭 Target step:', stepId);

//     if (!currentUrl.includes('/brainstorming')) {
//       console.log('🧭 Navigating to brainstorming route first');
//       this.router.navigate(['/product/brainstormer/brainstorming']).then(() => {
//         console.log('✅ Navigation to brainstorming complete, setting step');
//         this.stepperService.goToStepById(stepId);
//       });
//     } else {
//       console.log('🧭 Already in brainstorming, just changing step');
//       this.stepperService.goToStepById(stepId);
//     }
//   }

//   // Data transformation
//   getCustomFeatures(): CustomFeature[] {
//     if (!this.summaryData?.features) {
//       return [];
//     }
//     return this.summaryData.features.map(feature => ({
//       name: feature.name,
//       color: feature.color,
//       category: feature.category
//     }));
//   }

//   // UI helpers
//   getPlaceholderText(step: string): string {
//     switch (step) {
//       case 'understanding':
//         return 'Complete the Understanding step to see market insights here.';
//       case 'personas':
//         return 'Complete the User Persona step to see personas here.';
//       case 'features':
//         return 'Complete the Feature List step to see features here.';
//       case 'swot':
//         return 'Complete the SWOT Analysis step to see analysis here.';
//       case 'roadmap':
//         return 'Complete the Roadmap step to see timeline here.';
//       default:
//         return 'No data available yet.';
//     }
//   }
// }

