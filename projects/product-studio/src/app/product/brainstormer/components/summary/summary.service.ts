import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, Subscription } from 'rxjs';
import { map, distinctUntilChanged, shareReplay, tap } from 'rxjs/operators';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { PersonaDataService } from '../../services/persona-data.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';
import {
  PersonaData,
  SWOTData,
  FeaturesData,
  RoadmapData
} from '../../interfaces/pipeline-api.interface';

// Summary-specific interfaces
export interface SummaryPersona {
  name: string;
  role: string;
  avatarUrl: string;
}

export interface SummaryFeature {
  name: string;
  color: string;
  category: 'must_have' | 'should_have' | 'could_have' | 'wont_have';
}

export interface SummarySwotItem {
  category: 'strengths' | 'weaknesses' | 'opportunities' | 'threats';
  points: string[];
  color: string;
}

export interface SummaryTimelineItem {
  icon: string;
  label: string;
}

export interface SummaryTimelineQuarter {
  quarter: string;
  items: SummaryTimelineItem[];
}

export interface SummaryData {
  name: string;
  description: string;
  progress: number;
  contributionText: string;
  understandingText: string;
  features: SummaryFeature[];
  personas: SummaryPersona[];
  swot: SummarySwotItem[];
  timeline: SummaryTimelineQuarter[];
  isLoading: boolean;
  hasData: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SummaryService implements OnDestroy {
  private static readonly SESSION_STORAGE_KEY = 'summary_data';

  private readonly summaryDataSubject = new BehaviorSubject<SummaryData>(this.getDefaultSummaryData());
  public readonly summaryData$ = this.summaryDataSubject.asObservable();

  private subscriptions: Subscription[] = [];

  // Color mappings for different categories
  private readonly FEATURE_COLORS = {
    must_have: '#0F9D57',    // Green
    should_have: '#FDC100',  // Yellow
    could_have: '#FD7542',   // Orange
    wont_have: '#25364D'     // Dark Blue
  };

  private readonly SWOT_COLORS = {
    strengths: '#0F9D57',     // Green
    opportunities: '#FDC100', // Yellow
    weaknesses: '#FD7542',    // Orange
    threats: '#FF4444'        // Red
  };

  constructor(
    private appStateService: AppStateService,
    private pipelineService: ProductPipelineService,
    private personaDataService: PersonaDataService,
    private roadmapDataService: RoadmapDataService
  ) {

    // Initialize reactive data stream first
    this.initializeSummaryData();

    // Setup roadmap data subscription for real-time updates
    this.setupRoadmapDataSubscription();
    this.forceDataRefresh();

    // Load from session storage after a small delay to ensure observables are set up
    setTimeout(() => {
      this.loadFromSessionStorage();
    }, 100);
  }

  private initializeSummaryData(): void {
    // Subscribe to pipeline state changes and transform data
    combineLatest([
      this.appStateService.pipelineState$,
      this.appStateService.loadingState$
    ]).pipe(
      map(([pipelineState, loadingState]) => this.transformPipelineDataToSummary(pipelineState, loadingState.isLoadingPipeline)),
      distinctUntilChanged((prev, curr) => {
        // More granular comparison to ensure updates when data changes
        const prevKey = `${prev.name}-${prev.progress}-${prev.hasData}-${prev.features.length}-${prev.personas.length}`;
        const currKey = `${curr.name}-${curr.progress}-${curr.hasData}-${curr.features.length}-${curr.personas.length}`;
        return prevKey === currKey;
      }),
      shareReplay(1),
      tap(summaryData => {
        this.saveToSessionStorage(summaryData);
      })
    ).subscribe(summaryData => {
      this.summaryDataSubject.next(summaryData);
    });
  }

  private transformPipelineDataToSummary(pipelineState: any, isLoading: boolean): SummaryData {

    if (isLoading || !pipelineState) {
      return this.getDefaultSummaryData();
    }

    // Extract project details with fallback chain
    const name = pipelineState.project_name || pipelineState.name || 'Untitled Project';
    const description = pipelineState.project_description || pipelineState.description || 'No description available';
    

    const progress = this.calculateProgress(pipelineState);
    const features = this.extractFeatures(pipelineState.data?.features || pipelineState.features);
    const personas = this.extractPersonas(pipelineState.data?.persona || pipelineState.persona);
    const swot = this.extractSwotAnalysis(pipelineState.data?.swot || pipelineState.swot);
    const timeline = this.extractTimeline(pipelineState.data?.roadmap || pipelineState.roadmap);

    return {
      name,
      description,
      progress,
      contributionText: this.generateContributionText(pipelineState),
      understandingText: this.extractUnderstandingText(pipelineState.data),
      features,
      personas,
      swot,
      timeline,
      isLoading: false,
      hasData: this.hasAnyPipelineData(pipelineState.data)
    };
  }

  /**
   * Check if there's any valid pipeline data
   */
  private hasAnyPipelineData(data: any): boolean {
    
    if (!data) {
      return false;
    }

    const dataPoints = [
      'market_research',
      'lbc',
      'persona',
      'swot',
      'features',
      'roadmap'
    ];

    const hasData = dataPoints.some(point => {
      const hasPoint = !!(data[point] && Object.keys(data[point]).length > 0);
      return hasPoint;
    });

    return hasData;
  }

  /**
   * Extract and format project description
   */
  private extractProjectDescription(pipelineState: any): string {
    
    // Try different possible locations for description
    const sources = [
      { data: pipelineState.data?.market_research?.market_summary, type: 'market summary' },
      { data: pipelineState.data?.lbc?.value_proposition, type: 'value proposition' },
      { data: pipelineState.description, type: 'direct description' },
      { data: pipelineState.data?.description, type: 'nested description' }
    ];

    for (const source of sources) {
      if (source.data) {
        const description = Array.isArray(source.data) 
          ? source.data.join('. ')
          : source.data;
        return this.truncateText(description, 200);
      }
    }
    
    return 'Project description will be available after completing the understanding step.';
  }

  private calculateProgress(pipelineState: any): number {
    
    // Define the steps we're tracking
    const steps = ['market_research', 'lbc', 'persona', 'swot', 'features', 'roadmap'];
    const totalSteps = steps.length;
    
    // Count completed steps based on data presence
    let completedSteps = 0;
    const data = pipelineState.data || {};
    
    steps.forEach(step => {
      if (data[step] && Object.keys(data[step]).length > 0) {
        completedSteps++;
      }
    });

    // Calculate percentage
    const progress = Math.round((completedSteps / totalSteps) * 100);
    
    return progress;
  }

  private generateContributionText(pipelineState: any): string {
    const progress = this.calculateProgress(pipelineState);

    if (progress === 0) {
      return 'Ready to start your brainstorming journey!';
    } else if (progress < 50) {
      return `Great start! You've completed ${progress}% of your brainstorming session.`;
    } else if (progress < 100) {
      return `Excellent progress! You're ${progress}% through your brainstorming session.`;
    } else {
      return `Congratulations! You've completed your brainstorming session with ${progress}% progress.`;
    }
  }

  private extractUnderstandingText(data: any): string {
    const marketData = data?.market_research;
    const lbcData = data?.lbc;
    
    if (marketData) {
      let understanding = '';
      
      if (marketData.market_summary) {
        understanding += this.truncateText(marketData.market_summary, 100);
      }
      
      if (marketData.identified_gaps) {
        understanding += understanding ? ' ' : '';
        understanding += this.truncateText(marketData.identified_gaps, 100);
      }
      
      return understanding || 'Market research insights will appear here.';
    }
    
    if (lbcData?.problem && lbcData.problem.length > 0) {
      return this.truncateText(lbcData.problem.join('. '), 200);
    }
    
    return 'Understanding insights will be available after completing the market research step.';
  }

  private extractFeatures(featuresData?: FeaturesData): SummaryFeature[] {
    if (!featuresData) {
      return [];
    }

    const features: SummaryFeature[] = [];
    
    // Extract titles from each MoSCoW category
    if (featuresData.must_have) {
      features.push(...featuresData.must_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.must_have,
        category: 'must_have' as const
      })));
    }
    
    if (featuresData.should_have) {
      features.push(...featuresData.should_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.should_have,
        category: 'should_have' as const
      })));
    }
    
    if (featuresData.could_have) {
      features.push(...featuresData.could_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.could_have,
        category: 'could_have' as const
      })));
    }
    
    if (featuresData.wont_have) {
      features.push(...featuresData.wont_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.wont_have,
        category: 'wont_have' as const
      })));
    }
    
    return features;
  }

  private extractPersonas(personaData?: PersonaData): SummaryPersona[] {
    if (!personaData?.personas) {
      return [];
    }

    return personaData.personas.slice(0, 4).map((persona, index) => {
      // Use gender-based avatar mapping from PersonaDataService
      const avatarUrl = persona.avatar ||
        this.personaDataService.getGenderBasedAvatar(
          (persona as any).gender, // Cast to access gender field
          persona.role,
          index
        );

      return {
        name: this.truncateText(persona.name, 15),
        role: this.truncateText(persona.role, 18),
        avatarUrl: avatarUrl
      };
    });
  }

  private extractSwotAnalysis(swotData?: SWOTData): SummarySwotItem[] {
    if (!swotData) {
      return [];
    }

    const swotItems: SummarySwotItem[] = [];
    
    if (swotData.strengths && swotData.strengths.length > 0) {
      swotItems.push({
        category: 'strengths',
        color: this.SWOT_COLORS.strengths,
        points: swotData.strengths.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.weaknesses && swotData.weaknesses.length > 0) {
      swotItems.push({
        category: 'weaknesses',
        color: this.SWOT_COLORS.weaknesses,
        points: swotData.weaknesses.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.opportunities && swotData.opportunities.length > 0) {
      swotItems.push({
        category: 'opportunities',
        color: this.SWOT_COLORS.opportunities,
        points: swotData.opportunities.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.threats && swotData.threats.length > 0) {
      swotItems.push({
        category: 'threats',
        color: this.SWOT_COLORS.threats,
        points: swotData.threats.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    return swotItems;
  }

  private extractTimeline(roadmapData?: RoadmapData): SummaryTimelineQuarter[] {
    if (!roadmapData?.project_tasks || roadmapData.project_tasks.length === 0) {
      return this.getDefaultTimeline();
    }

    // Transform roadmap data into timeline quarters
    const quarterMap = new Map<number, SummaryTimelineItem[]>();

    // Process each task from the roadmap data
    roadmapData.project_tasks.forEach((apiTask: any, index: number) => {
      const quarter = apiTask.quarter || 1; // Default to quarter 1 if not specified
      const taskName = apiTask.task || `Task ${index + 1}`;

      // Get appropriate icon based on task name/type
      const icon = this.getTaskIcon(taskName, apiTask.priority);

      const timelineItem: SummaryTimelineItem = {
        icon: icon,
        label: taskName
      };

      // Add to the appropriate quarter
      if (!quarterMap.has(quarter)) {
        quarterMap.set(quarter, []);
      }
      quarterMap.get(quarter)!.push(timelineItem);
    });

    // Convert map to SummaryTimelineQuarter array
    const timelineQuarters: SummaryTimelineQuarter[] = [];

    // Ensure we have at least 4 quarters, even if some are empty
    for (let q = 1; q <= Math.max(4, Math.max(...quarterMap.keys())); q++) {
      const quarterTasks = quarterMap.get(q) || [];

      timelineQuarters.push({
        quarter: `Quarter ${q}`,
        items: quarterTasks
      });
    }

    return timelineQuarters.length > 0 ? timelineQuarters : this.getDefaultTimeline();
  }

  private getDefaultTimeline(): SummaryTimelineQuarter[] {
    return [
      {
        quarter: 'Quarter 1',
        items: [
          { icon: 'awe_research', label: 'Market Research' },
          { icon: 'awe_planning', label: 'Project Planning' },
          { icon: 'awe_design', label: 'Initial Design' }
        ]
      },
      {
        quarter: 'Quarter 2',
        items: [
          { icon: 'awe_prototype', label: 'Prototype Development' },
          { icon: 'awe_test', label: 'Testing & Validation' }
        ]
      },
      {
        quarter: 'Quarter 3',
        items: [
          { icon: 'awe_code', label: 'Development' },
          { icon: 'awe_review', label: 'Quality Review' }
        ]
      },
      {
        quarter: 'Quarter 4',
        items: [
          { icon: 'awe_deploy', label: 'Launch Preparation' },
          { icon: 'awe_business', label: 'Market Launch' }
        ]
      }
    ];
  }

  private truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * Get appropriate icon for a task based on its name and priority
   */
  private getTaskIcon(taskName: string, priority?: string): string {
    const lowerTaskName = taskName.toLowerCase();

    // Map task types to icons based on common keywords
    if (lowerTaskName.includes('research') || lowerTaskName.includes('analysis')) {
      return 'awe_research';
    }
    if (lowerTaskName.includes('design') || lowerTaskName.includes('ui') || lowerTaskName.includes('ux')) {
      return 'awe_design';
    }
    if (lowerTaskName.includes('develop') || lowerTaskName.includes('code') || lowerTaskName.includes('implement')) {
      return 'awe_code';
    }
    if (lowerTaskName.includes('test') || lowerTaskName.includes('qa') || lowerTaskName.includes('quality')) {
      return 'awe_test';
    }
    if (lowerTaskName.includes('deploy') || lowerTaskName.includes('launch') || lowerTaskName.includes('release')) {
      return 'awe_deploy';
    }
    if (lowerTaskName.includes('plan') || lowerTaskName.includes('strategy') || lowerTaskName.includes('roadmap')) {
      return 'awe_planning';
    }
    if (lowerTaskName.includes('prototype') || lowerTaskName.includes('mvp') || lowerTaskName.includes('demo')) {
      return 'awe_prototype';
    }
    if (lowerTaskName.includes('market') || lowerTaskName.includes('business') || lowerTaskName.includes('sales')) {
      return 'awe_business';
    }
    if (lowerTaskName.includes('document') || lowerTaskName.includes('spec') || lowerTaskName.includes('requirement')) {
      return 'awe_document';
    }
    if (lowerTaskName.includes('review') || lowerTaskName.includes('feedback') || lowerTaskName.includes('approval')) {
      return 'awe_review';
    }

    // Default icons based on priority if no keyword match
    if (priority) {
      const lowerPriority = priority.toLowerCase();
      if (lowerPriority === 'high') return 'awe_priority_high';
      if (lowerPriority === 'medium') return 'awe_priority_medium';
      if (lowerPriority === 'low') return 'awe_priority_low';
    }

    // Default fallback icon
    return 'awe_task';
  }

  private getDefaultSummaryData(): SummaryData {
    return {
      name: 'Loading...',
      description: 'Loading project data...',
      progress: 0,
      contributionText: 'Preparing your brainstorming session...',
      understandingText: 'Understanding insights will appear here.',
      features: [],
      personas: [],
      swot: [],
      timeline: [],
      isLoading: true,
      hasData: false
    };
  }

  /**
   * Get current summary data synchronously
   */
  getCurrentSummaryData(): SummaryData {
    return this.summaryDataSubject.value;
  }

  /**
   * Check if specific step data is available
   */
  hasStepData(step: string): boolean {
    const currentData = this.summaryDataSubject.value;
    switch (step) {
      case 'understanding':
        return currentData.understandingText !== 'Understanding insights will appear here.';
      case 'personas':
        return currentData.personas.length > 0;
      case 'features':
        return currentData.features.length > 0;
      case 'swot':
        return currentData.swot.length > 0;
      case 'roadmap':
        return currentData.timeline.length > 0;
      default:
        return false;
    }
  }

  /**
   * Get current project name from pipeline state
   */
  getProjectName(): string {
    return this.pipelineService.getProjectName() || '';
  }

  /**
   * Get project name as observable for reactive updates
   */
  getProjectName$(): Observable<string> {
    return this.pipelineService.getProjectName$().pipe(
      map(projectName => projectName || '')
    );
  }

  /**
   * Set project name through pipeline service
   */
  setProjectName(projectName: string): void {
    this.pipelineService.setProjectName(projectName);
  }

  /**
   * Force refresh of summary data
   * Useful when navigating to summary after completing steps
   */
  refreshSummaryData(): void {
    const currentPipelineState = this.appStateService.pipelineState;

    // Trigger pipeline state update to refresh all observables
    this.appStateService.updatePipelineState({
      ...currentPipelineState,
      lastUpdated: new Date().toISOString()
    });

  }

  /**
   * Save summary data to session storage
   */
  private saveToSessionStorage(summaryData: SummaryData): void {
    try {
      const dataToStore = {
        ...summaryData,
        timestamp: new Date().toISOString()
      };
      sessionStorage.setItem(SummaryService.SESSION_STORAGE_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('❌ Failed to save summary data to session storage:', error);
    }
  }

  /**
   * Load summary data from session storage
   */
  private loadFromSessionStorage(): void {
    try {
      const storedData = sessionStorage.getItem(SummaryService.SESSION_STORAGE_KEY);

      if (storedData) {
        const parsedData = JSON.parse(storedData);

        // Remove timestamp before using the data
        const { timestamp, ...summaryData } = parsedData;

        // Only use stored data if it's recent (within 1 hour)
        const storedTime = new Date(timestamp);
        const now = new Date();
        const hoursDiff = (now.getTime() - storedTime.getTime()) / (1000 * 60 * 60);


        if (hoursDiff < 1) {
          this.summaryDataSubject.next(summaryData);
        } else {
          this.clearSessionStorage();
        }
      } else {
      }
    } catch (error) {
      console.error('❌ Failed to load summary data from session storage:', error);
      this.clearSessionStorage();
    }
  }

  /**
   * Clear session storage
   */
  private clearSessionStorage(): void {
    try {
      sessionStorage.removeItem(SummaryService.SESSION_STORAGE_KEY);
    } catch (error) {
      console.error('❌ Failed to clear session storage:', error);
    }
  }

  /**
   * Setup subscription to roadmap data changes
   */
  private setupRoadmapDataSubscription(): void {
    const roadmapSubscription = this.roadmapDataService.originalApiData$.subscribe(roadmapData => {
      if (roadmapData) {
        this.refreshSummaryData();
      }
    });

    this.subscriptions.push(roadmapSubscription);
  }

  /**
   * Get summary data from session storage (public method for components)
   */
  public getSummaryDataFromStorage(): SummaryData | null {
    try {
      const storedData = sessionStorage.getItem(SummaryService.SESSION_STORAGE_KEY);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        const { timestamp, ...summaryData } = parsedData;
        return summaryData;
      }
    } catch (error) {
      console.error('❌ Failed to get summary data from session storage:', error);
    }
    return null;
  }

  /**
   * Force update session storage with current data
   */
  public updateSessionStorage(): void {
    const currentData = this.summaryDataSubject.value;
    this.saveToSessionStorage(currentData);
  }

  /**
   * Force immediate data refresh and emit to subscribers
   */
  public forceDataRefresh(): void {

    // Get current pipeline state
    const currentPipelineState = this.appStateService.pipelineState;
    const currentLoadingState = this.appStateService.loadingState;

    // Transform and emit immediately
    const refreshedData = this.transformPipelineDataToSummary(
      currentPipelineState,
      currentLoadingState.isLoadingPipeline
    );


    // Save to session storage
    this.saveToSessionStorage(refreshedData);

    // Emit to subscribers immediately
    this.summaryDataSubject.next(refreshedData);

    // Force another emission after a small delay to ensure subscribers receive it
    // setTimeout(() => {
    //   this.summaryDataSubject.next(refreshedData);
    // }, 50);

  }

  /**
   * OnDestroy implementation
   */
  ngOnDestroy(): void {
    // this.subscriptions.forEach(sub => sub.unsubscribe());
    // this.subscriptions = [];
  }
}
