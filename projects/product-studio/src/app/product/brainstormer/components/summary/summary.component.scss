:host {
  display: block;
  background-color: #f7f7f9;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

// --- GENERAL STYLING ---
.project-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;

  &::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #8a2be2, #6a5acd);
  }
}

.task-label{
  font-size: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;
}

// --- PROJECT TITLE EDITING STYLES ---
.project-title-container {
  .project-title-display {
    .edit-title-btn {
      opacity: 0;
      transition: opacity 0.2s ease;
      border: none;
      background: transparent;
      padding: 4px;
      border-radius: 4px;

      &:hover {
        background-color: rgba(0, 123, 255, 0.1);
      }

      .edit-icon {
        width: 16px;
        height: 16px;
      }
    }

    &:hover .edit-title-btn {
      opacity: 1;
    }
  }

  .project-title-edit {
    .project-title-input {
      font-size: 2.5rem;
      font-weight: bold;
      border: 2px solid #007bff;
      border-radius: 8px;
      padding: 8px 12px;
      background-color: #fff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);

      &:focus {
        outline: none;
        border-color: #0056b3;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.4);
      }

      &::placeholder {
        color: #6c757d;
        opacity: 0.7;
      }
    }

    .save-title-btn,
    .cancel-title-btn {
      border: none;
      background: transparent;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      .save-icon,
      .cancel-icon {
        width: 20px;
        height: 20px;
      }
    }

    .save-title-btn:hover {
      background-color: rgba(40, 167, 69, 0.1);
    }

    .cancel-title-btn:hover {
      background-color: rgba(220, 53, 69, 0.1);
    }
  }
}

// Responsive adjustments for project title editing
@media (max-width: 768px) {
  .project-title-container {
    .project-title-edit {
      .project-title-input {
        font-size: 1.8rem;
      }
    }
  }

  .project-title {
    font-size: 1.8rem;
  }
}

.project-description {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.6;
}

.btn-icon {
  background: none;
  width: 50px;
  height: 50px;
  border: none;
  color: #adb5bd;
  cursor: pointer;
  // margin: 0;
  transition: color 0.2s ease;
  &:hover {
    color: #6c757d;
  }
}

.card-wrapper {
  border-radius: 24px;
  border: 1px solid rgba(124, 185, 254, 0.9);
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  box-shadow: 0px 0px 16px 0px rgba(225, 225, 225, 0.25);
  backdrop-filter: blur(25px);
}

// --- PROGRESS SECTION ---
.progress-heading {
  font-size: 1.1rem;
  font-weight: 600;
}
.progress-subheading {
  font-size: 0.9rem;
  color: #6c757d;
}
.export-btn {
  border-radius: 8px;
  border: 1px solid var(--Primary-50, #f2ebfd);
  background: var(
    --Main-gradient,
    linear-gradient(
      109deg,
      var(--Primary-500, #7c3aed) 4.7%,
      var(--Secondary-500, #2563eb) 94.91%
    )
  );
  color: #fff;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  white-space: nowrap;
  &:hover {
    background-color: darken(#6f42c1, 5%);
  }
}

// Circular Progress Bar
@property --progress {
  syntax: "<integer>";
  initial-value: 0;
  inherits: false;
}

.progress-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: grid;
  place-items: center;
  background: conic-gradient(#8a2be2 calc(var(--progress) * 1%), #e9ecef 0);
  transition: --progress 1s ease-out;
  animation: progress-animation 1s ease-out forwards;

  .progress-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
  }
}

@keyframes progress-animation {
  from {
    --progress: 0;
  }
}

// --- CARD-SPECIFIC STYLING ---
h4 {
  font-size: 1.25rem;
  font-weight: 600;
}

.export-button-wrapper{
  position: relative;
  top: -20px;
  left: 1751px;
}

// Feature List
.feature-list {
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-around;
  // gap: 1.25rem;
  .feature-tag {
    padding: 0.3rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1.5px solid;

    &.yellow {
      border-color: #ffc107;
      background-color: #fff9e6;
    }
    &.light-green {
      border-color: #28a745;
      background-color: #eaf6ec;
    }
    &.light-orange {
      border-color: #fd7e14;
      background-color: #fff2e8;
    }
    &.gray {
      border-color: #6c757d;
      background-color: #f8f9fa;
    }
    // Add other colors as needed
  }
}

.task-bar {
  // height: 45px;
  border-radius: 8px;
  width: 25%;
  background: #eeeff0;
  
}

// User Persona
.persona-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding-bottom: 1rem;
}

.persona-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  // padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .persona-avatar {
    flex-shrink: 0;

    .avatar-image {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #e9ecef;
    }
  }

  .persona-info {
    flex: 1;
    min-width: 0; // Allow text truncation

    .persona-name {
      font-size: 0.95rem;
      font-weight: 600;
      color: #212529;
      margin-bottom: 0.25rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }

    .persona-role {
      font-size: 0.8rem;
      color: #6c757d;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }
  }
}

// Theme support
:host-context(.dark-theme) {
  .persona-card {
    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .persona-info {
      .persona-name {
        color: #f8f9fa;
      }

      .persona-role {
        color: #adb5bd;
      }
    }

    .persona-avatar .avatar-image {
      border-color: #495057;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .persona-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .persona-card {
    .persona-avatar .avatar-image {
      width: 44px;
      height: 44px;
    }

    .persona-info {
      .persona-name {
        font-size: 0.9rem;
      }

      .persona-role {
        font-size: 0.75rem;
      }
    }
  }
}

// Summary Cards Grid Layout
.summary-cards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 1rem;
  height: 518px; // 2 * 249px + 20px gap
}

.summary-card-container {
  display: flex;
  width: 100%;
  height: 249px;
}

.summary-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .card-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

// Roadmap Full Height
.roadmap-card {
  height: 518px; // Match the total height of 4 cards
  display: flex;
  flex-direction: column;

  .roadmap-content {
    flex: 1;
    overflow-y: auto;
  }
}

// SWOT Analysis
.swot-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0.75rem;
  height: 140px; // Reduced to fit within card
  margin-bottom: 1rem;
}

.swot-quadrant {
  position: relative;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  display: flex;

  .swot-border {
    width: 6px;
    flex-shrink: 0;
  }

  .swot-content {
    flex: 1;
    padding: 5px;
    display: flex;
    flex-direction: column;
  }
}

.swot-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;

  .swot-item {
    position: relative;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.3;
    color: #495057;

    &::before {
      content: "•";
      position: absolute;
      left: 0;
      font-size: 1rem;
      line-height: 1.3;
      color: #6c757d;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Dark theme support
[data-theme="dark"] {
  .swot-quadrant {
    background-color: #2d3748;

    .swot-item {
      color: #e2e8f0;

      &::before {
        color: #a0aec0;
      }
    }
  }
}

// Responsive Design
@media (max-width: 991.98px) {
  .summary-cards-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 249px);
    height: auto;
    gap: 1rem;
  }

  .roadmap-card {
    height: 400px; // Smaller height on mobile
    margin-top: 1rem;
  }
}

@media (max-width: 767.98px) {
  .summary-card-container {
    height: 220px; // Slightly smaller on mobile
  }

  .summary-cards-grid {
    grid-template-rows: repeat(4, 220px);
  }

  .swot-grid {
    height: 120px; // Even smaller SWOT grid on mobile
  }
}



.my-custom-bar {
  --progress-color: #743eed;
  --trail-color: #d5c3ff;
  --text-color: #575555;
  --size: 190px;
  --thickness: -16px;
}
button {
  margin-top: 1rem;
}
 :host {
  display: block;
  width: 100%;
}

h4 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}
.btn-export{
  border-radius: 12px;
  border: none;
  width: auto;
  background-color: #743eed;
  color: #fff;
  font-weight: 500; 

  &:hover {
    background-color: #6f42c1;
  }
}

.footer-arrow-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #d0d5dd;
  background: none;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}


// --- TIMELINE CONTAINER & MAIN LINE ---
.timeline-container {
  position: relative;
  width: 100%;
}

.timeline-main-line {
  position: absolute;
  left: 50%;
  top: 10px;
  bottom: 10px;
  width: 4px;
  transform: translateX(-50%);
  background: linear-gradient(180deg, #f0e6ff 0%, #e9f3ff 100%);
  border-radius: 2px;
  z-index: 1;
}


// --- TIMELINE ITEM BASE STYLES ---
.timeline-item {
  position: relative;
  width: 50%;

  // The colored dot on the timeline
  &::after {
    content: '';
    position: absolute;
    top: 6px; // Adjust vertical alignment
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #eab9c9; // Pink/rose color of the dot
    z-index: 2; // On top of the main line
  }

  // The horizontal connecting line
  &::before {
    content: '';
    position: absolute;
    top: 12px; // Align with the center of the dot
    height: 2px;
    width: 2.5rem; // Length of the horizontal line
    background: #eab9c9;
    z-index: 1;
  }

  // Remove padding from the last item
  &:last-child {
    padding-bottom: 0;
  }
}


// --- ALTERNATING LAYOUT LOGIC ---

// ODD items (1, 3, 5) are positioned on the LEFT side
.timeline-item:nth-child(odd) {
  left: 0;
  padding-right: 2.5rem;
  
  .timeline-content {
    text-align: right;
    align-items: flex-end; // Align internal items to the right
  }
  
  &::after { right: -7px; } // Position dot on its right edge
  &::before { right: 0; }   // Position line on its right edge
}

// EVEN items (2, 4) are positioned on the RIGHT side
.timeline-item:nth-child(even) {
  left: 50%;
  padding-left: 2.5rem;
  
  .timeline-content {
    text-align: left;
    align-items: flex-start; // Align internal items to the left
  }
  
  &::after { left: -7px; }  // Position dot on its left edge
  &::before { left: 0; }    // Position line on its left edge
}


// --- CONTENT STYLING ---

.timeline-content {
  display: flex;
  flex-direction: column;
}

.quarter-title {
  font-size: 1rem;
  font-weight: 600;
  color: #8A2BE2;
  margin-bottom: 0.75rem;
}

.quarter-tasks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-item {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.95rem;

  awe-icons {
    color: #8A2BE2;
  }
}


// --- RESPONSIVE STYLES for mobile screens ---
@media (max-width: 768px) {
  // Move the main line to the far left
  .timeline-main-line {
    left: 7px; // Half the width of the dot
    transform: none;
  }

  // Make all items full-width and aligned to the left
  .timeline-item,
  .timeline-item:nth-child(even) {
    width: 100%;
    left: 0;
    padding-left: 2.5rem; // Add padding to all items
    padding-right: 0;
  }
  
  // Align all content to the left
  .timeline-item .timeline-content,
  .timeline-item:nth-child(odd) .timeline-content {
    text-align: left;
    align-items: flex-start;
  }
  
  // Position all dots and lines to the left
  .timeline-item::after,
  .timeline-item:nth-child(odd)::after,
  .timeline-item:nth-child(even)::after {
    left: 0;
  }
  
  .timeline-item::before,
  .timeline-item:nth-child(odd)::before,
  .timeline-item:nth-child(even)::before {
    left: 14px; // Start the line after the dot
  }
}

// Export buttons group
.export-buttons-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

// Individual export icon buttons
.export-icon-btn {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  border: 1px solid #e9ecef;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  
  &:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    border-color: #dee2e6;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f8f9fa;
  }
  
  .export-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .export-buttons-group {
    gap: 0.5rem;
  }
  
  .export-icon-btn {
    width: 45px;
    height: 45px;
    
    .export-icon {
      width: 20px;
      height: 20px;
    }
  }
}