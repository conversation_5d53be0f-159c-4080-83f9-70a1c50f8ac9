<!-- Debug info -->
<div *ngIf="!summaryData" class="p-4">
  <!-- <h3>Debug: No Summary Data</h3>
  <p>summaryData is null or undefined</p>
  <button (click)="forceRefreshWithChangeDetection()" class="btn btn-primary">Force Refresh</button> -->
</div>

<div class="project-summary-container p-4 p-md-5" *ngIf="summaryData">
  <!-- Debug info -->
  <!-- Debug info - always visible for testing -->
  <!-- <div class="mb-3 p-2 bg-light border rounded" style="font-size: 12px;">
    <strong>Debug Info:</strong>
    hasData: {{summaryData.hasData}},
    isLoading: {{summaryData.isLoading}},
    name: {{summaryData.name}},
    features: {{summaryData.features?.length || 0}},
    personas: {{summaryData.personas?.length || 0}},
    swot: {{summaryData.swot?.length || 0}},
    timeline: {{summaryData.timeline?.length || 0}}
    <button (click)="forceRefreshWithChangeDetection()" class="btn btn-sm btn-secondary ms-2">Refresh Data</button>
  </div> -->

  <!-- Main Content - Always show when summaryData exists -->
  <div>
    <!-- TOP SECTION: Title, Description, and Progress -->
    <div class="row mb-5">
      <div class="col-12 col-lg-12 col-md-12">
        <!-- Project Title with Edit Functionality -->
        <div class="project-title-container d-flex align-items-center gap-2 mb-2">
          <div *ngIf="!isEditingTitle" class="project-title-display d-flex align-items-center gap-2">
            <h4 class="project-title mb-0">{{
              getFormattedTitle()
              }}</h4>
            <button type="button" class="btn btn-link p-0 edit-title-btn" (click)="startEditingTitle()"
              title="Edit project name" aria-label="Edit project name">
              <awe-icons iconName="awe_edit" iconColor="blue" class="edit-icon"></awe-icons>
            </button>
          </div>

          <div *ngIf="isEditingTitle" class="project-title-edit d-flex align-items-center gap-2 w-100">
            <input #titleInput type="text" class="form-control project-title-input" [value]="getProjectName()"
              (keyup.enter)="saveTitle(titleInput.value)" (keyup.escape)="cancelEditingTitle()"
              (blur)="saveTitle(titleInput.value)" placeholder="Edit project name if you want!" maxlength="100"
              autocomplete="off" />
            <button type="button" class="btn btn-link p-0 save-title-btn" (click)="saveTitle(titleInput.value)"
              title="Save project name" aria-label="Save project name">
              <awe-icons iconName="awe_check" iconColor="success" class="save-icon"></awe-icons>
            </button>
            <button type="button" class="btn btn-link p-0 cancel-title-btn" (click)="cancelEditingTitle()"
              title="Cancel editing" aria-label="Cancel editing">
              <awe-icons iconName="awe_close" iconColor="danger" class="cancel-icon"></awe-icons>
            </button>
          </div>
        </div>

        <awe-caption type="regular" class="project-description">{{
          summaryData.description || 'Loading project description...'
          }}</awe-caption>
      </div>
    </div>
    <div class="row gap-2">
      <div class="col-12 d-flex align-items-center justify-content-between mt-4 mt-lg-0">
        <div class="progress-wrapper d-flex align-items-center">
          <awe-progress-bar [progress]="summaryData.progress || 0" class="my-custom-bar"></awe-progress-bar>

          <div class="ms-4">
            <awe-heading variant="s2" type="bold" class="progress-heading">{{
              summaryData.progress === 100
              ? "Congratulations! Session Complete"
              : "Great Progress!"
              }}</awe-heading>
            <awe-heading variant="s2" type="regular" class="progress-subheading"
              [innerHTML]="summaryData.contributionText"></awe-heading>
          </div>
        </div>

        <!-- Export Buttons -->
        <div class="export-buttons-group d-flex gap-3">
          <button class="export-icon-btn" (click)="exportToJira()" [disabled]="false" title="Export Roadmap to Jira">
            <img src="assets/icons/jira.svg" alt="Jira" class="export-icon">
          </button>

          <button class="export-icon-btn" (click)="exportToCsv()" [disabled]="false" title="Export Roadmap to CSV">
            <img src="assets/icons/csv-logo.svg" alt="CSV" class="export-icon">
          </button>

          <button class="export-icon-btn" (click)="exportToExperienceStudio()" [disabled]="true"
            title="Export to Experience Studio (Disabled)">
            <img src="assets/studio-logo/experience-studio.svg" alt="Experience Studio" class="export-icon">
          </button>
        </div>
      </div>
    </div>

    <!-- MAIN GRID OF CARDS -->
    <div class="row g-4 mt-4">
      <!-- Left Column - 4 Equal Cards -->
      <div class="col-12 col-lg-8">
        <div class="summary-cards-grid">
          <!-- Understanding -->
          <div class="summary-card-container">
            <awe-card [applyBodyPadding]="true" [showFooter]="true" class="summary-card">
              <div awe-card-header-content class="d-flex justify-content-between px-4 pt-4">
                <h4>Understanding</h4>
                <button (click)="redirectToUnderstanding()" class="btn-icon m-0">
                  <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
                </button>
              </div>
              <div class="px-4 card-content">
                <p class="" *ngIf="hasStepData('understanding')">
                  {{ summaryData.understandingText }}
                </p>
                <p class="px-4 text-muted" *ngIf="!hasStepData('understanding')">
                  {{ getPlaceholderText("understanding") }}
                </p>
              </div>
              <!-- <div awe-card-footer-content class="d-flex justify-content-end">
                <button (click)="redirectToUnderstanding()" class="btn-icon">
                  <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
                </button>
              </div> -->
            </awe-card>
          </div>

          <!-- UserPersona -->
          <div class="summary-card-container">
            <awe-card [applyBodyPadding]="true" [showFooter]="true" class="summary-card">
              <div awe-card-header-content class="d-flex justify-content-between px-4 pt-4">
                <h4>User Personas</h4>
                <button (click)="redirectToPersona()" class="btn-icon m-0">
                  <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
                </button>
              </div>
              <div class="card-content">
                <div class="persona-grid px-4 pt-3" *ngIf="hasStepData('personas')">
                  <div *ngFor="let persona of summaryData.personas" class="persona-card">
                    <div class="persona-avatar">
                      <img [src]="persona.avatarUrl" [alt]="persona.name" class="avatar-image" />
                    </div>
                    <div class="persona-info">
                      <div class="persona-name" [title]="persona.name">{{ persona.name }}</div>
                      <div class="persona-role" [title]="persona.role">{{ persona.role }}</div>
                    </div>
                  </div>
                </div>
                <div class="px-4 pt-4" *ngIf="!hasStepData('personas')">
                  <p class="text-muted">
                    {{ getPlaceholderText("personas") }}
                  </p>
                </div>
              </div>
            </awe-card>
          </div>

          <!-- Feature List -->
          <div class="summary-card-container">
            <awe-card [applyBodyPadding]="true" [showFooter]="true" class="summary-card">
              <div class="card-content">

                <div awe-card-header-content class="d-flex justify-content-between px-4 pt-4">
                  <!-- <h4>Feature List</h4> -->
                  <div class="pb-2" *ngIf="hasStepData('features')">
                    <app-custom-feature-list [features]="getCustomFeatures()" [showDropdown]="true">
                    </app-custom-feature-list>
                  </div>
                  <button (click)="redirectToFeature()" class="btn-icon m-0">
                    <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
                  </button>
                </div>

                <div class="px-4 pt-4" *ngIf="!hasStepData('features')">
                  <p class="text-muted">
                    {{ getPlaceholderText("features") }}
                  </p>
                </div>
              </div>
              <!-- <div awe-card-footer-content class="d-flex justify-content-end">
               
              </div> -->
            </awe-card>
          </div>

          <!-- SWOT Analysis -->
          <div class="summary-card-container">
            <awe-card [applyBodyPadding]="true" [showFooter]="true" class="summary-card">
              <div awe-card-header-content class="d-flex justify-content-between px-4 pt-4">
                <h4>SWOT Analysis</h4>
                <button (click)="redirectToSwot()" class="btn-icon m-0">
                  <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
                </button>
              </div>
              <div class="card-content">
                <div class="swot-grid px-4 pt-3" *ngIf="hasStepData('swot')">
                  <div *ngFor="let item of summaryData.swot" class="swot-quadrant" [class]="'swot-' + item.category">
                    <div class="swot-border" [style.background-color]="item.color"></div>
                    <div class="swot-content">
                      <ul class="swot-list">
                        <li *ngFor="let point of item.points" class="swot-item" [title]="point">
                          <p class="m-0">{{ point }}</p>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="px-4 pt-4" *ngIf="!hasStepData('swot')">
                  <p class="text-muted">
                    {{ getPlaceholderText("swot") }}
                  </p>
                </div>

              </div>

            </awe-card>
          </div>
        </div>
      </div>

      <!-- Right Column - Full Height Roadmap -->
      <div class="col-12 col-lg-4">
        <awe-card [applyBodyPadding]="false" [showFooter]="true" class="roadmap-card">
          <!-- Card Header -->
          <div awe-card-header-content class="p-4">
            <h4>Roadmap</h4>
          </div>

          <!-- Card Body with Timeline -->
          <div class="p-4 pt-0 roadmap-content">
            <div class="timeline-container" *ngIf="hasStepData('roadmap')">
              <!-- The main vertical line running down the center -->
              <div class="timeline-main-line"></div>
              <!-- Loop through each quarter -->
              <div *ngFor="
                  let quarter of summaryData.timeline;
                  let isLast = last
                " class="timeline-item">
                <div class="timeline-content">
                  <h4 class="quarter-title">{{ quarter.quarter }}</h4>
                  <div class="quarter-tasks">
                    <div *ngFor="let task of quarter.items" class="task-item">
                      <!-- <awe-icons [iconName]="task.icon" iconSize="16px"></awe-icons> -->
                      <p class="task-label" [title]="task.label">{{ task.label }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="!hasStepData('roadmap')" class="text-center py-4">
              <p class="text-muted">
                {{ getPlaceholderText("roadmap") }}
              </p>
            </div>
            <button (click)="redirectToRoadmap()" class="btn-icon">
              <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
            </button>
          </div>

          <!-- Card Footer -->
          <!-- <div awe-card-footer-content class="d-flex justify-content-end">
            
          </div> -->
        </awe-card>
      </div>
    </div>
  </div>
  <!-- Close main content div -->
</div>