import { Component, ViewChild, ElementRef, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ChatMessage } from '../../../shared/interfaces/app-state.interface';
import { Observable, Subscription, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { SplitScreenService } from '../../services/split-screen.service';
import { Router } from '@angular/router';
import { UnderstandingDataService } from '../../services/understanding-data.service';
import { PersonaDataService } from '../../services/persona-data.service';
import { SwotDataService } from '../../services/swot-data.service';
import { FeatureDataService } from '../../services/export-service/feature-data.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';



@Component({
  selector: 'app-chat-panel',
  imports: [CommonModule, FormsModule, MarkdownModule],
  templateUrl: './chat-panel.component.html',
  styleUrl: './chat-panel.component.scss'
})
export class ChatPanelComponent implements OnInit, OnDestroy {
  // State observables
  messages$: Observable<ChatMessage[]>;
  isAiTyping$: Observable<boolean>;
  isLoading$: Observable<boolean>;
  errorMessage$: Observable<string | null>;

  // Local properties
  currentMessage: string = '';
  roboBallIcon: string = 'icons/robo_ball.svg';
  aweSendIcon: string = 'assets/icons/awe_send.svg';
  private subscriptions: Subscription[] = [];

  // Typewriter animation properties
  typewriterMessages: Map<string, string> = new Map();
  private typewriterSubjects: Map<string, Subject<void>> = new Map();
  private destroy$ = new Subject<void>();

  // Track if component has been initialized to prevent typewriter on restored messages
  private isInitialized: boolean = false;

  @ViewChild('chatContent') chatContent!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Output event for closing split screen
  @Output() closeSplitScreen = new EventEmitter<void>();

  constructor(
    private pipelineService: ProductPipelineService,
    private appStateService: AppStateService,
    private splitScreenService: SplitScreenService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private understandingDataService: UnderstandingDataService,
    private personaDataService: PersonaDataService,
    private swotDataService: SwotDataService,
    private featureDataService: FeatureDataService,
    private roadmapDataService: RoadmapDataService
  ) {
    // Initialize state observables
    this.messages$ = this.appStateService.selectChatMessages();
    this.isAiTyping$ = this.appStateService.chatState$.pipe(
      map(state => state.isAiTyping)
    );
    this.isLoading$ = this.appStateService.chatState$.pipe(
      map(state => state.isLoading)
    );
    this.errorMessage$ = this.appStateService.chatState$.pipe(
      map(state => state.errorMessage)
    );
  }

  ngOnInit(): void {
    // Subscribe to chat state to restore input text
    this.subscriptions.push(
      this.appStateService.chatState$.subscribe(chatState => {
        // Restore input text if available
        if (chatState.currentInputText && chatState.currentInputText !== this.currentMessage) {
          this.currentMessage = chatState.currentInputText;
        }
      })
    );

    // Subscribe to messages for auto-scrolling and typewriter animation
    this.subscriptions.push(
      this.messages$.subscribe((messages) => {
        // Handle typewriter animation for new AI messages only (not restored ones)
        messages.forEach(message => {
          if (message.sender === 'ai' &&
              !this.typewriterMessages.has(message.id) &&
              !message.isRestored) { // Don't animate restored messages
            this.startTypewriterAnimation(message);
          } else if (message.isRestored && !this.typewriterMessages.has(message.id)) {
            // For restored messages, immediately set full content without animation
            this.typewriterMessages.set(message.id, message.content);
          }
        });
        setTimeout(() => this.scrollToBottom(), 100);
      })
    );

    // Mark component as initialized after first render
    setTimeout(() => {
      this.isInitialized = true;
    }, 100);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.typewriterSubjects.forEach(subject => {
      subject.next();
      subject.complete();
    });
  }



  sendMessage(event?: any) {
    const inputElement = event?.target as HTMLInputElement;
    const message = inputElement?.value?.trim() || this.currentMessage?.trim();

    if (!message) {
      return;
    }


    // Clear input immediately and sync with state
    if (inputElement) {
      inputElement.value = '';
    }
    this.currentMessage = '';
    this.appStateService.clearChatInputText();

    // Send message through pipeline service (which handles state updates)
    this.pipelineService.sendChatMessage(message).subscribe({
      next: (response) => {

        // Handle payload if present
        this.handleChatPayload(response.payload);
      },
      error: (error) => {
        console.error('❌ Failed to send chat message:', error);
        // On error, restore the message so user can try again
        this.currentMessage = message;
        this.appStateService.updateChatInputText(message);
        if (inputElement) {
          inputElement.value = message;
        }
      }
    });
  }



  private scrollToBottom(): void {
    if (this.chatContent) {
      const element = this.chatContent.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  /**
   * Handle input changes and sync with state for persistence
   */
  onMessageInput(event: any): void {
    const inputElement = event.target as HTMLInputElement;
    const value = inputElement?.value || '';
    this.currentMessage = value;
    // Sync input text with state for persistence across reloads
    this.appStateService.updateChatInputText(value);
  }

  // Method to handle close split screen
  onCloseSplitScreen(): void {
    this.splitScreenService.closeSplitScreen();
    this.closeSplitScreen.emit();
  }

  /**
   * Get display label for response type
   */
  getResponseTypeLabel(responseType: string): string {
    switch (responseType) {
      case 'clarification':
        return '❓ Clarification';
      case 'answer':
        return '✅ Answer';
      case 'error':
        return '❌ Error';
      default:
        return '💬 Response';
    }
  }

  /**
   * Start typewriter animation for AI message
   */
  private startTypewriterAnimation(message: ChatMessage): void {
    const messageId = message.id;
    const fullContent = message.content;

    // Initialize empty content for typewriter effect
    this.typewriterMessages.set(messageId, '');

    // Create a subject to control this specific animation
    const animationSubject = new Subject<void>();
    this.typewriterSubjects.set(messageId, animationSubject);

    // Animation parameters
    const wordsPerSecond = 8; // Adjust speed as needed
    const words = fullContent.split(' ');
    const intervalMs = 1000 / wordsPerSecond;

    let currentWordIndex = 0;

    const typeNextWord = () => {
      if (currentWordIndex < words.length) {
        const currentContent = words.slice(0, currentWordIndex + 1).join(' ');
        this.typewriterMessages.set(messageId, currentContent);
        this.cdr.detectChanges();
        currentWordIndex++;

        setTimeout(() => typeNextWord(), intervalMs);
      } else {
        // Animation complete
        this.typewriterMessages.set(messageId, fullContent);
        this.typewriterSubjects.delete(messageId);
        animationSubject.complete();
        this.cdr.detectChanges();
      }
    };

    // Start the animation
    setTimeout(() => typeNextWord(), 300); // Small delay before starting
  }

  /**
   * Get the current typewriter content for a message
   */
  getTypewriterContent(messageId: string, originalContent: string): string {
    return this.typewriterMessages.get(messageId) || originalContent;
  }

  /**
   * Check if message is currently being typed
   */
  isMessageTyping(messageId: string): boolean {
    return this.typewriterSubjects.has(messageId);
  }

  /**
   * Handle payload data from chat response with step-aware routing
   */
  private handleChatPayload(payload: any): void {
    if (payload && Object.keys(payload).length > 0) {

      // Get current step from URL or pipeline state
      const currentStep = this.getCurrentStep();

      // Route data to appropriate service based on current step and payload content
      this.routeDataToServices(payload, currentStep);
    }
  }

  /**
   * Get current step from URL or pipeline state
   * Since all steps are under /brainstorming, we need to get the step from the stepper service
   */
  private getCurrentStep(): string {
    const url = this.router.url;

    // Check if we're in the brainstorming flow
    if (url.includes('/brainstorming')) {
      // Get current step from pipeline state and map to stepper step names
      const pipelineState = this.appStateService.pipelineState;
      const currentStep = pipelineState.current_step;

      // Map pipeline steps to stepper step names for data routing
      const pipelineToStepperMap: Record<string, string> = {
        'lbc': 'understanding',
        'persona': 'persona',
        'swot': 'swot',
        'features': 'features',
        'roadmap': 'roadmap'
      };

      const stepperStep = currentStep ? pipelineToStepperMap[currentStep] : 'understanding';
      return stepperStep || 'understanding';
    }

    // Fallback to understanding if not in brainstorming flow
    return 'understanding';
  }

  /**
   * Route chat payload data to appropriate data services
   */
  private routeDataToServices(payload: any, currentStep: string): void {

    try {
      // Route based on current step context
      switch (currentStep) {
        case 'understanding':
          this.handleUnderstandingData(payload);
          break;
        case 'persona':
          this.handlePersonaData(payload);
          break;
        case 'swot':
          this.handleSwotData(payload);
          break;
        case 'features':
          this.handleFeatureData(payload);
          break;
        case 'roadmap':
          this.handleRoadmapData(payload);
          break;
        default:
          console.warn(`⚠️ Unknown step for data routing: ${currentStep}`);
          // Try to detect data type from payload structure
          this.handleDataByType(payload);
      }
    } catch (error) {
      console.error('❌ Error routing chat payload data:', error);
    }
  }

  /**
   * Handle Understanding/LBC data updates
   */
  private handleUnderstandingData(payload: any): void {
    if (payload.business_model_canvas || payload.canvas || payload.understanding) {
      const canvasData = payload.business_model_canvas || payload.canvas || payload.understanding;
      this.understandingDataService.updateBusinessModelCanvas(canvasData);
    }
  }

  /**
   * Handle Persona data updates
   */
  private handlePersonaData(payload: any): void {
    if (payload.personas || payload.user_personas) {
      const personaData = payload.personas || payload.user_personas;
      this.personaDataService.updatePersonasFromAPI(personaData);
    }
  }

  /**
   * Handle SWOT data updates
   */
  private handleSwotData(payload: any): void {
    if (payload.swot || payload.strengths || payload.weaknesses || payload.opportunities || payload.threats) {
      const swotData = payload.swot || payload;
      this.swotDataService.updateFromApiResponse(swotData);
    }
  }

  /**
   * Handle Feature data updates
   */
  private handleFeatureData(payload: any): void {
    if (payload.features || payload.must_have || payload.should_have || payload.could_have || payload.wont_have) {
      const featureData = payload.features || payload;
      this.featureDataService.updateFromApiResponse(featureData);
    }
  }

  /**
   * Handle Roadmap data updates
   */
  private handleRoadmapData(payload: any): void {
    if (payload.roadmap || payload.tasks || payload.quarters) {
      const roadmapData = payload.roadmap || payload.tasks || payload;
      this.roadmapDataService.updateFromApiResponse(roadmapData);
    }
  }

  /**
   * Fallback: Try to detect data type from payload structure
   */
  private handleDataByType(payload: any): void {
    // Try to detect data type from payload keys
    const keys = Object.keys(payload);

    if (keys.some(key => ['business_model_canvas', 'canvas', 'understanding'].includes(key))) {
      this.handleUnderstandingData(payload);
    } else if (keys.some(key => ['personas', 'user_personas'].includes(key))) {
      this.handlePersonaData(payload);
    } else if (keys.some(key => ['swot', 'strengths', 'weaknesses', 'opportunities', 'threats'].includes(key))) {
      this.handleSwotData(payload);
    } else if (keys.some(key => ['features', 'must_have', 'should_have', 'could_have', 'wont_have'].includes(key))) {
      this.handleFeatureData(payload);
    } else if (keys.some(key => ['roadmap', 'tasks', 'quarters'].includes(key))) {
      this.handleRoadmapData(payload);
    } else {
    }
  }
}
