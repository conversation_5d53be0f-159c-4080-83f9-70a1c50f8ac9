import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  ChangeDetectorRef,
} from '@angular/core';
import { Router } from '@angular/router';
import { ButtonComponent, PromptBarComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { buttonLabels, fileOptions } from '../constants/promt-screen.constant';
import {
  FileAttachPillComponent,
  FileAttachOption,
} from '../../../../shared/components/file-attach-pill/file-attach-pill.component';

import { HeroSectionHeaderComponent } from '../../../shared/components/hero-section-header/hero-section-header.component';
import {
  IconStatus,
  Buttons,
  SelectedFile,
} from '../modals/promt-screen.modal';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { ProjectDetailsResponse } from '../../interfaces/pipeline-api.interface';
import { LoadingComponent } from '../loading/loading.component';
import { TokenStorageService } from '@shared/index';
import { AppStateService } from '../../../shared/services/app-state.service';

@Component({
  selector: 'app-promt-screen',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HeroSectionHeaderComponent,
    PromptBarComponent,
    ButtonComponent,
    IconsComponent,
    FileAttachPillComponent,
    LoadingComponent,
  ],
  templateUrl: './promt-screen.component.html',
  styleUrls: ['./promt-screen.component.scss'],
})
export class PromtScreenComponent implements OnInit, OnDestroy {
  fileOptions: FileAttachOption[] = fileOptions;
  selectedFile: File | null = null;
  selectedFiles: SelectedFile[] = [];
  selectedFileName = '';
  previewFile: SelectedFile | null = null;
  leftIcons: { name: string; status: IconStatus; color: string }[] = [];
  rightIcons: { name: string; status: IconStatus; color: string }[] = [];
  currentPrompt = '';
  @Input() buttons: Buttons[] = buttonLabels;
  submissionData = {
    prompt: this.currentPrompt,
    timestamp: new Date().toISOString(),
    imageFile: this.selectedFile,
    imageUrl: this.selectedFile ? URL.createObjectURL(this.selectedFile) : null,
    imageDataUri: null as string | null,
    fileName: this.selectedFileName || null,
  };

  getIconColor(): string {
    const isDisabled =
      !this.currentPrompt?.trim() ||
      this.isEnhancing ||
      this.enhanceClickCount >= this.maxEnhanceClicks;
    return `var(--icon-${isDisabled ? 'disabled' : 'enabled'}-color)`;
  }
  // Properties
  theme: 'light' | 'dark' = 'light';
  animatedTexts = [
    'Brainstorm a new idea',
    'Help me improve user engagement for my app.',
    'Create user personas for Fly high airlines',
  ];

   get userName(): string {
    const userName = this.tokenStorageService.getDaName() || 'Hi User';
    return userName.split(' ')[0];

  }

  // File handling

  readonly maxAllowedFiles = 1;
  isFileAttachDisabled = true; // Permanently disabled
  fileError = '';
  showPreview = false;

  // State flags
  isEnhancing = false;
  isGenerating = false;
  isEnhancedPromptValid = true;
  isPromptEnhanced = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;



  // Loading state
  showLoading = false;
  loadingTitle = 'Starting Your Brainstorming Session';
  loadingMessage =
    'Please wait while we initialize your project and gather market insights...';
  loadingSteps = [
    'Initializing project',
    'Gathering market research',
    'Preparing workspace',
  ];
  currentLoadingStep = 0;

  private subscriptions: Subscription[] = [];
  private boundHandlePasteEvent: (event: ClipboardEvent) => void;

  constructor(
    private router: Router,
    private cdr: ChangeDetectorRef,
    private pipelineService: ProductPipelineService,
    private tokenStorageService: TokenStorageService,
    private appStateService: AppStateService,
  ) {
    this.boundHandlePasteEvent = this.handlePasteEvent.bind(this);
  }
  private handlePasteEvent(event: ClipboardEvent): void {
    // Optionally handle pasted images or text here
    // Example: Prevent default paste if image is present
    if (event.clipboardData) {
      const items = event.clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          // Handle pasted image if needed
          event.preventDefault();
          // You can implement image handling logic here
        }
      }
    }
  }


  onFileOptionSelected(option: FileAttachOption): void {
    // Prevent any file operations when disabled
    if (this.isFileAttachDisabled) {
      return;
    }

    if (this.isProcessing() || this.isMaxFilesReached()) {
      this.fileError = `Maximum ${this.maxAllowedFiles} image(s) and ${this.maxAllowedDocuments} document(s) allowed`;
      return;
    }
    if (option.value === 'computer') {
      this.handleEnhancedAlternate();
      return;
    }
    if (option.value === 'url') {
      if (this.isEnhancing || this.isGenerating) return;
      const url = prompt('Enter the URL of the file:');
      if (!url) return;
      const mockFile: SelectedFile = {
        id: Math.random().toString(36).substring(2, 11),
        name: url.split('/').pop() || 'file.jpg',
        url,
        type: 'image/jpeg',
      };
      this.selectedFiles = [...this.selectedFiles, mockFile];
      this.updateFileAttachPillStatus();
    }
  }
  closePreview(): void {
    this.previewFile = null;
    this.showPreview = false;
  }
  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
  }
  private handleEnhancedAlternate(): void {
    // Prevent file operations when disabled
    if (this.isFileAttachDisabled) {
      return;
    }

    if (this.isEnhancing || this.isGenerating) {
      return;
    }
    if (this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) =>
      this.handleFileSelect(event),
    );
    fileInput.click();
  }
  private isMaxFilesReached(): boolean {
    // Check both image files and document files
    const totalFiles = this.selectedFiles.length + this.attachedDocuments.length;
    return totalFiles >= this.maxAllowedFiles + this.maxAllowedDocuments;
  }
  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    // Accept both images and documents
    fileInput.accept = 'image/*,.pdf,.txt,.doc,.docx,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }
  ngOnInit(): void {
    this.resetComponentState();
    this.initTheme();

    // Detect system theme preference
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
    this.theme = prefersDarkScheme.matches ? 'dark' : 'light';

    const mediaQueryListener = (event: MediaQueryListEvent) => {
      this.theme = event.matches ? 'dark' : 'light';
      this.cdr.detectChanges();
    };

    prefersDarkScheme.addEventListener('change', mediaQueryListener);
    this.subscriptions.push(
      new Subscription(() => {
        prefersDarkScheme.removeEventListener('change', mediaQueryListener);
      }),
    );

    // Subscribe to loading state for enhancement feedback
    this.subscriptions.push(
      this.appStateService.loadingState$.subscribe(loadingState => {
        // Update local isEnhancing state based on pipeline loading
        if (loadingState.isLoadingPipeline && loadingState.loadingMessage?.includes('Enhancing')) {
          this.isEnhancing = true;
        } else if (!loadingState.isLoadingPipeline && this.isEnhancing) {
          this.isEnhancing = false;
        }
        this.cdr.detectChanges();
      })
    );
  }
  handleIconClick(event: { name: string; side: string; index: number }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        this.handleEnhancedSend();
        break;
      default:
        console.warn('Unknown icon clicked:', event.name);
    }
  }
  onFileRemoved(fileId: string): void {
    this.removeFile(fileId);
    // this.promptService.resetEnhancedPromptState();
    // this.promptService.setImage(null);
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.currentPrompt = '';
    // this.updateSendButtonState();
  }
  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(
      (file) => file.id !== fileId,
    );
    this.updateFileAttachPillStatus();
  }
  ngAfterViewInit(): void {
    document.addEventListener('paste', this.boundHandlePasteEvent);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    document.removeEventListener('paste', this.boundHandlePasteEvent);
  }

  handleEnterPressed(): void {
    if (this.isProcessing() || !this.currentPrompt?.trim()) {
      return;
    }
    this.handleEnhancedSend();
  }

  private updateIconDisabledState(): void {
    requestAnimationFrame(() => {
      const isEmptyPrompt = !this.currentPrompt?.trim();
      const isProcessing = this.isProcessing();

      // Update file attach pill state
      const fileAttachPill = document.querySelector('awe-file-attach-pill');
      if (fileAttachPill) {
        fileAttachPill.classList.toggle('disabled', this.isFileAttachDisabled || isProcessing);
      }

      // Update enhance and send button states
      const enhanceIcons = document.querySelectorAll<HTMLElement>(
        '.enhance-icons awe-icons',
      );
      enhanceIcons.forEach((icon, index) => {
        const isEnhanceButton = index === 0;
        const isSendButton = index === 1;
        let isDisabled = isEmptyPrompt || isProcessing;

        if (isEnhanceButton) {
          // Enhance button: disabled if empty prompt, processing, or max clicks reached
          isDisabled =
            isEmptyPrompt ||
            isProcessing ||
            this.enhanceClickCount >= this.maxEnhanceClicks;
        } else if (isSendButton) {
          // Send button: disabled if empty prompt, processing, or (enhanced but invalid)
          if (this.isPromptEnhanced) {
            isDisabled =
              isEmptyPrompt || isProcessing || !this.isEnhancedPromptValid;
          } else {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        }

        icon.classList.toggle('disabled', isDisabled);
        icon.style.cssText = `
          color: var(${isDisabled ? '--icon-disabled-color' : '--icon-enabled-color'});
          cursor: ${isDisabled ? 'not-allowed' : 'pointer'};
          opacity: ${isDisabled ? 'var(--icon-disabled-opacity, 0.9)' : '1'};
        `;
      });
    });
  }
  handleEnhancedSend(): void {
    if (this.isEnhancing || !this.currentPrompt?.trim()) return;
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return;
    }

    const userIdea = this.currentPrompt.trim();
    this.isGenerating = true;
    this.showLoading = true;
    this.currentLoadingStep = 0;

    // Simulate loading steps progression
    const stepInterval = setInterval(() => {
      if (this.currentLoadingStep < this.loadingSteps.length - 1) {
        this.currentLoadingStep++;
      }
    }, 1500);

    // Step 1: Call identify details API to extract project information
    this.pipelineService.getIdentifyDetails(userIdea).subscribe({
      next: (response: ProjectDetailsResponse) => {
        clearInterval(stepInterval);
        this.currentLoadingStep = this.loadingSteps.length - 1;

        // Wait a moment to show completion, then navigate to brainstormer form
        setTimeout(() => {
          this.isGenerating = false;
          this.showLoading = false;

          // Navigate to brainstormer-form
          // The form will be pre-populated via the service
          this.router.navigate(['brainstormer-form'], {
            relativeTo: this.router.routerState.root.firstChild?.firstChild?.firstChild,
          });
        }, 1000);
        this.currentPrompt = '';

      },
      error: (error) => {
        console.error('Error identifying project details:', error);
        clearInterval(stepInterval);
        this.isGenerating = false;
        this.showLoading = false;

        alert('Failed to analyze your idea. Please try again.');
      },
    });
  }

  handleEnhanceText(): void {
    if (
      this.enhanceClickCount >= this.maxEnhanceClicks ||
      !this.currentPrompt?.trim()
    )
      return;

    this.isEnhancing = true;
    this.updateIconDisabledState();

    // Call the actual enhance API
    this.pipelineService.enhancePrompt(this.currentPrompt).subscribe({
      next: (response) => {

        // Format the enhanced prompt from the response
        const enhancedPrompt = this.formatEnhancedPrompt(response);

        // Update the current prompt with enhanced version
        this.currentPrompt = enhancedPrompt;
        this.isPromptEnhanced = true;
        this.isEnhancedPromptValid = true;
        this.enhanceClickCount++;

        // Update UI
        this.cdr.detectChanges();
        this.adjustTextareaHeight();
        this.updateIconDisabledState();
      },
      error: (error) => {
        console.error('❌ Failed to enhance prompt:', error);

        // Show error message to user
        alert('Failed to enhance prompt. Please try again.');

        // Reset enhancement state
        this.isPromptEnhanced = false;
        this.isEnhancedPromptValid = true;
      },
      complete: () => {
        this.isEnhancing = false;
        this.updateIconDisabledState();
        this.cdr.detectChanges();
      }
    });
  }

  handleSuggestionClick(suggestion: string): void {
    this.currentPrompt = suggestion.replace(/^✨\s*/, '').trim();
    this.adjustTextareaHeight();
  }

  /**
   * Format the enhanced prompt response into a readable string
   */
  private formatEnhancedPrompt(response: any): string {
    try {
      const { enhancedPrompt } = response;

      if (!enhancedPrompt) {
        return this.currentPrompt; // Fallback to original prompt
      }

      // Format the enhanced prompt into a structured text
      const sections = [
        `**Idea Title:** ${enhancedPrompt.ideaTitle}`,
        `**Concept Overview:** ${enhancedPrompt.conceptOverview}`,
        `**Problem:** ${enhancedPrompt.problem}`,
        `**Target Audience:** ${enhancedPrompt.targetAudience}`,
        `**Core Solution:** ${enhancedPrompt.coreSolution}`,
        `**Unique Value Proposition:** ${enhancedPrompt.uniqueValueProposition}`,
        `**Key Success Metric:** ${enhancedPrompt.keySuccessMetric}`,
        `**Monetization Model:** ${enhancedPrompt.monetizationModel}`
      ];

      return sections.join('\n\n');
    } catch (error) {
      console.error('Error formatting enhanced prompt:', error);
      return this.currentPrompt; // Fallback to original prompt
    }
  }

  handleFileAttach(): void {
    // Prevent file operations when disabled
    if (this.isFileAttachDisabled) {
      return;
    }

    if (this.isProcessing() || this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }

    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    fileInput.click();
  }

  handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !this.validateFile(file)) return;

    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.selectedFile = file;
    this.selectedFileName = file.name;

    // Check if it's an image or document
    const isImage = file.type.startsWith('image/');
    const isDocument = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ].includes(file.type);

    if (isImage) {
      // Handle image files - add to selectedFiles for preview
      const fileUrl = URL.createObjectURL(file);
      this.selectedFiles = [
        {
          id: Math.random().toString(36).substring(2, 11),
          name: file.name,
          url: fileUrl,
          type: file.type,
        },
      ];
    } else if (isDocument) {
      // Handle document files - add to attachedDocuments
      const document: AttachedDocument = {
        id: Math.random().toString(36).substring(2, 11),
        name: file.name,
        type: file.type,
        size: file.size,
        file: file
      };
      this.attachedDocuments = [...this.attachedDocuments, document];
    }

    this.updateFileAttachPillStatus();
    this.currentPrompt = '';
    input.value = '';
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const dotIndex = filename.lastIndexOf('.');

    if (filename.length <= maxLength || dotIndex === -1) return filename;

    const extension = filename.slice(dotIndex);
    const nameWithoutExt = filename.slice(0, dotIndex);
    const truncatedName = nameWithoutExt.slice(
      0,
      maxLength - extension.length - 3,
    );

    return `${truncatedName}...${extension}`;
  }

  // Tech/Design selection

  // Utility methods
  isProcessing(): boolean {
    return this.isEnhancing || this.isGenerating;
  }

  // isMaxFilesReached(): boolean {
  //   return this.selectedFiles.length >= this.maxAllowedFiles;
  // }

  private validateFile(file: File): boolean {
    const acceptedImageTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];

    const acceptedDocumentTypes = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const allAcceptedTypes = [...acceptedImageTypes, ...acceptedDocumentTypes];

    if (!file.type) {
      this.fileError = 'Folders cannot be uploaded';
    } else if (!allAcceptedTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) and document files (PDF, TXT, DOC, DOCX) are allowed';
    } else if (file.size > 10 * 1024 * 1024) { // Increased to 10MB for documents
      this.fileError = 'File size must be less than 10MB';
    } else {
      this.fileError = '';
      return true;
    }

    return false;
  }

  private resetComponentState(): void {
    this.currentPrompt = '';
    this.selectedFiles = [];
    this.attachedDocuments = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.previewFile = null;
    this.isFileAttachDisabled = true; // Keep permanently disabled
    this.fileError = '';
    this.isEnhancing = false;
    this.isGenerating = false;
    this.showPreview = false;
    this.enhanceClickCount = 0;
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;
  }

  private updateFileAttachPillStatus(): void {
    const isMaxReached = this.isMaxFilesReached();
    const uploadIcon = this.leftIcons.find(
      (icon) => icon.name === 'awe_enhanced_alternate',
    );
    if (uploadIcon) {
      uploadIcon.status = 'disable'; // Always disabled
    }
    // Keep file attach permanently disabled
    this.isFileAttachDisabled = true;
  }
  handlePromptChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.currentPrompt = input.value;

    // Reset enhancement state when user manually changes the prompt
    if (this.isPromptEnhanced) {
      this.isPromptEnhanced = false;
      this.isEnhancedPromptValid = true;
      this.enhanceClickCount = 0;
    }

    this.adjustTextareaHeight();
    this.updateIconDisabledState();
  }

  private adjustTextareaHeight(): void {
    setTimeout(() => {
      const textareas =
        document.querySelectorAll<HTMLTextAreaElement>('.prompt-text');
      if (!textareas.length) return;

      textareas.forEach((textarea) => {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      });
    }, 0);
  }

  private initTheme(): void {
    // This would normally subscribe to a theme service
    this.theme = 'light';
  }

  attachedDocuments: AttachedDocument[] = [];
  maxAllowedDocuments = 3;

  onDocumentFileSelected(file: File): void {
    if (this.attachedDocuments.length >= this.maxAllowedDocuments) {
      this.fileError = `Maximum ${this.maxAllowedDocuments} documents allowed`;
      return;
    }

    if (!this.validateDocumentFile(file)) {
      return;
    }

    const document: AttachedDocument = {
      id: Math.random().toString(36).substring(2, 11),
      name: file.name,
      type: file.type,
      size: file.size,
      file: file
    };

    this.attachedDocuments = [...this.attachedDocuments, document];
    this.fileError = '';
  }

  private validateDocumentFile(file: File): boolean {
    const acceptedTypes = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!acceptedTypes.includes(file.type)) {
      this.fileError = 'Only PDF, TXT, DOC, and DOCX files are allowed';
      return false;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      this.fileError = 'File size must be less than 10MB';
      return false;
    }

    return true;
  }

  getFileIcon(fileType: string): string {
    switch (fileType) {
      case 'application/pdf':
        return 'awe_picture_as_pdf';
      case 'text/plain':
        return 'awe_description';
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'awe_article';
      default:
        return 'awe_attach_file';
    }
  }

  removeDocument(index: number): void {
    this.attachedDocuments = this.attachedDocuments.filter((_, i) => i !== index);
  }

  /**
   * Get loading text for enhancement based on current state
   */
  getEnhanceLoadingText(): string {
    if (this.selectedFiles.length > 0) {
      return 'Analyzing your image and enhancing prompt...';
    }
    if (this.attachedDocuments.length > 0) {
      return 'Analyzing your documents and enhancing prompt...';
    }
    return 'Enhancing your prompt...';
  }
}

interface AttachedDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  file: File;
}
