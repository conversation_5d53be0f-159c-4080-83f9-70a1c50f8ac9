import { ProductPipelineService } from '../../services/product-pipeline.service';
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ProjectDetailsResponse } from '../../interfaces/pipeline-api.interface';

@Component({
  selector: 'app-new-right-panel-header',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './new-right-panel-header.component.html',
  styleUrl: './new-right-panel-header.component.scss'
})
export class NewRightPanelHeaderComponent implements OnInit, OnDestroy {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() isCollapsed: boolean = false;
  @Output() togglePanel = new EventEmitter<void>();
  @Output() nextStepClicked = new EventEmitter<void>();
  @Output() previousStepClicked = new EventEmitter<void>();

  // Dynamic content observables
  projectTitle$: Observable<string>;
  currentStep$: Observable<StepperStep | null>;
  currentStepIndex$: Observable<number>;
  totalSteps$: Observable<number>;
  canGoNext$: Observable<boolean>;
  canGoPrevious$: Observable<boolean>;

  // Current values for template
  projectTitle: string = '';
  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;
  totalSteps: number = 5;
  isHeaderLoading: boolean = false;

  // Loading state for pipeline next API
  isNextStepLoading: boolean = false;
  nextStepLoadingMessage: string = '';
  isNextButtonDisabled: boolean = false;



  private subscriptions: Subscription[] = [];

  constructor(
    private stepperService: StepperService,
    private appStateService: AppStateService,
    private productPipelineService: ProductPipelineService
  ) {
    // Initialize observables
    this.projectTitle$ = this.appStateService.pipelineState$.pipe(
      map(pipelineState => pipelineState.project_name || 'Untitled Project')
    );

    this.currentStep$ = this.stepperService.currentStep$;
    this.currentStepIndex$ = this.stepperService.currentStepIndex$;
    this.totalSteps$ = this.stepperService.steps$.pipe(
      map(steps => steps.length)
    );
    this.canGoNext$ = this.stepperService.canGoNext$;
    this.canGoPrevious$ = this.stepperService.canGoPrevious$;
  }

  ngOnInit(): void {
    // Subscribe to project title changes
    this.subscriptions.push(
      this.projectTitle$.subscribe(title => {
        this.projectTitle = title;
      })
    );

    // Subscribe to current step changes
    this.subscriptions.push(
      this.currentStep$.subscribe(step => {
        this.currentStep = step;
      })
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.currentStepIndex$.subscribe(index => {
        this.currentStepIndex = index;
      })
    );

    // Subscribe to total steps changes
    this.subscriptions.push(
      this.totalSteps$.subscribe(total => {
        this.totalSteps = total;
      })
    );

    // Subscribe to loading state changes for pipeline next API
    this.subscriptions.push(
      this.appStateService.loadingState$.subscribe(loadingState => {
        this.isNextStepLoading = loadingState.isLoadingStep;

        if (this.isNextStepLoading && loadingState.currentOperation) {
          // Get the next step name for loading message
          const nextStepName = this.getNextStepName();
          this.nextStepLoadingMessage = `Coming up - ${nextStepName}...`;
        } else {
          this.nextStepLoadingMessage = '';
        }

        // Disable next button during loading
        this.isNextButtonDisabled = this.isNextStepLoading;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onTogglePanel(): void {
    this.togglePanel.emit();
  }

  onNextStep(): void {
    this.nextStepClicked.emit();
  }

  onPreviousStep(): void {
    this.previousStepClicked.emit();
  }

  // Get formatted title for display
  getFormattedTitle(): string {
    let projectName: string;
    const stepName = this.currentStep?.label || 'Understanding';

    // First try to get from project details (for recent projects)
    const projectDetails = this.productPipelineService.getProjectDetails();
    if (projectDetails?.project_name) {
      projectName = projectDetails.project_name;
    } 
    // Then try pipeline state (for pipeline progression)
    else if (this.projectTitle) {
      projectName = this.projectTitle;
    }
    // Finally fallback to default
    else {
      projectName = 'Untitled Project';
    }

    return `${projectName} - ${stepName}`;
  }

  // Get current step number for pagination display
  getCurrentStepNumber(): number {
    return this.currentStepIndex + 1;
  }

  // Get next step name for loading message
  getNextStepName(): string {
    const nextStepIndex = this.currentStepIndex + 1;
    if (nextStepIndex < this.totalSteps) {
      const nextStep = this.stepperService.getStepByIndex(nextStepIndex);
      return nextStep?.label || 'Next Step';
    }
    return 'Next Step';
  }
}
