import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, map, distinctUntilChanged, shareReplay } from 'rxjs';
import {
  AppState,
  PipelineState,
  ChatState,
  NavigationState,
  LoadingState,
  ErrorState,
  UserSessionState,
  UIState,
  ChatMessage,

  ComputedSelectors,
  DEFAULT_APP_STATE,
  DEFAULT_PIPELINE_STATE,
  DEFAULT_CHAT_STATE,
  DEFAULT_NAVIGATION_STATE,
  DEFAULT_LOADING_STATE,
  DEFAULT_ERROR_STATE,
  DEFAULT_SESSION_STATE,
  DEFAULT_UI_STATE
} from '../interfaces/app-state.interface';
import { 
  PipelineStep, 
  ChatResponse,
  PipelineStepResponse,
  PipelineStartResponse
} from '../../brainstormer/interfaces/pipeline-api.interface';

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  // Hybrid State Management: BehaviorSubject + sessionStorage persistence
  private readonly STORAGE_KEY = 'aava_app_state';
  private readonly PERSISTED_KEYS: (keyof AppState)[] = ['pipeline', 'chat', 'navigation', 'session'];

  // Core State BehaviorSubjects with sessionStorage persistence
  private readonly appStateSubject = new BehaviorSubject<AppState>(this.loadInitialState());
  private readonly pipelineStateSubject = new BehaviorSubject<PipelineState>(DEFAULT_PIPELINE_STATE);
  private readonly chatStateSubject = new BehaviorSubject<ChatState>(DEFAULT_CHAT_STATE);
  private readonly navigationStateSubject = new BehaviorSubject<NavigationState>(DEFAULT_NAVIGATION_STATE);
  private readonly loadingStateSubject = new BehaviorSubject<LoadingState>(DEFAULT_LOADING_STATE);
  private readonly errorStateSubject = new BehaviorSubject<ErrorState>(DEFAULT_ERROR_STATE);
  private readonly sessionStateSubject = new BehaviorSubject<UserSessionState>(DEFAULT_SESSION_STATE);
  private readonly uiStateSubject = new BehaviorSubject<UIState>(DEFAULT_UI_STATE);

  // Public Observable Streams
  public readonly appState$ = this.appStateSubject.asObservable();
  public readonly pipelineState$ = this.pipelineStateSubject.asObservable();
  public readonly chatState$ = this.chatStateSubject.asObservable();
  public readonly navigationState$ = this.navigationStateSubject.asObservable();
  public readonly loadingState$ = this.loadingStateSubject.asObservable();
  public readonly errorState$ = this.errorStateSubject.asObservable();
  public readonly sessionState$ = this.sessionStateSubject.asObservable();
  public readonly uiState$ = this.uiStateSubject.asObservable();

  constructor() {
    this.initializeState();
    this.setupStateSync();
    this.setupAutoSave();
  }

  // ===========================================
  // INITIALIZATION AND SETUP
  // ===========================================

  private initializeState(): void {
    const initialState = this.loadInitialState();

    // Initialize individual state subjects with loaded data
    this.pipelineStateSubject.next(initialState.pipeline);
    this.chatStateSubject.next(initialState.chat);
    this.navigationStateSubject.next(initialState.navigation);
    this.loadingStateSubject.next(initialState.loading);
    this.errorStateSubject.next(initialState.error);
    this.sessionStateSubject.next(initialState.session);
    this.uiStateSubject.next(initialState.ui);


  }

  private setupStateSync(): void {
    // Combine all state subjects into main app state
    combineLatest([
      this.pipelineState$,
      this.chatState$,
      this.navigationState$,
      this.loadingState$,
      this.errorState$,
      this.sessionState$,
      this.uiState$
    ]).pipe(
      map(([pipeline, chat, navigation, loading, error, session, ui]) => ({
        pipeline,
        chat,
        navigation,
        loading,
        error,
        session,
        ui,
        lastUpdated: new Date()
      }))
    ).subscribe(state => {
      this.appStateSubject.next(state);
    });
  }

  private setupAutoSave(): void {
    // Auto-save to session storage when state changes
    this.appState$.subscribe(state => {
      this.saveStateToStorage(state);
    });
  }

  // ===========================================
  // STATE PERSISTENCE (Hybrid: BehaviorSubject + sessionStorage)
  // ===========================================

  private loadInitialState(): AppState {
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);


        // Merge with defaults to ensure all properties exist
        return {
          ...DEFAULT_APP_STATE,
          ...parsedState,
          lastUpdated: new Date()
        };
      }
    } catch (error) {
      console.warn('⚠️ Failed to load state from storage:', error);
    }

    return DEFAULT_APP_STATE;
  }

  private saveStateToStorage(state: AppState): void {
    try {
      // Only save persisted keys to reduce storage size and improve performance
      const persistedState: any = {};
      this.PERSISTED_KEYS.forEach(key => {
        persistedState[key] = state[key];
      });

      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(persistedState));

    } catch (error) {
      console.warn('⚠️ Failed to save state to storage:', error);
    }
  }

  public clearStoredState(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY);

    } catch (error) {
      console.warn('⚠️ Failed to clear stored state:', error);
    }
  }

  // ===========================================
  // STATE MANAGEMENT (Hybrid: BehaviorSubject + sessionStorage)
  // ===========================================

  // ===========================================
  // STATE GETTERS
  // ===========================================

  public get currentState(): AppState {
    return this.appStateSubject.value;
  }

  public get pipelineState(): PipelineState {
    return this.pipelineStateSubject.value;
  }

  public get chatState(): ChatState {
    return this.chatStateSubject.value;
  }

  public get navigationState(): NavigationState {
    return this.navigationStateSubject.value;
  }

  public get loadingState(): LoadingState {
    return this.loadingStateSubject.value;
  }

  public get errorState(): ErrorState {
    return this.errorStateSubject.value;
  }

  public get sessionState(): UserSessionState {
    return this.sessionStateSubject.value;
  }

  public get uiState(): UIState {
    return this.uiStateSubject.value;
  }

  // ===========================================
  // STATE SELECTORS
  // ===========================================

  public selectPipelineData<T = any>(dataKey?: keyof PipelineState['data']): Observable<T | null> {
    return this.pipelineState$.pipe(
      map(state => dataKey ? state.data[dataKey] as T : state.data as T),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectCurrentStepData<T = any>(): Observable<T | null> {
    return combineLatest([this.pipelineState$, this.navigationState$]).pipe(
      map(([pipeline]) => {
        const currentStep = pipeline.current_step;
        return currentStep ? pipeline.data[currentStep] as T : null;
      }),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectChatMessages(): Observable<ChatMessage[]> {
    return this.chatState$.pipe(
      map(state => state.messages),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectIsLoading(): Observable<boolean> {
    return this.loadingState$.pipe(
      map(state => state.isLoadingPipeline || state.isLoadingStep || state.isLoadingChat),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectHasErrors(): Observable<boolean> {
    return this.errorState$.pipe(
      map(state => !!(state.pipelineError || state.stepError || state.chatError || state.networkError)),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectStepProgress(): Observable<number> {
    return this.navigationState$.pipe(
      map(state => {
        const totalSteps = 5; // understanding, persona, swot, features, roadmap
        return (state.completedSteps.length / totalSteps) * 100;
      }),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectHasActiveSession(): Observable<boolean> {
    return this.pipelineState$.pipe(
      map(state => !!state.run_id),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectProjectTitle(): Observable<string> {
    return this.pipelineState$.pipe(
      map(state => state.project_name || 'Untitled Project'),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  public selectProjectTitleWithStep(): Observable<string> {
    return combineLatest([
      this.pipelineState$,
      this.navigationState$
    ]).pipe(
      map(([pipeline, navigation]) => {
        const projectName = pipeline.project_name || 'Untitled Project';
        const currentStepId = navigation.currentStepId;

        // Map step IDs to display names
        const stepDisplayNames: Record<string, string> = {
          'understanding': 'Understanding',
          'persona': 'Persona',
          'swot': 'SWOT Analysis',
          'features': 'Features',
          'roadmap': 'Roadmap'
        };

        const stepName = stepDisplayNames[currentStepId] || 'Understanding';
        return `${projectName} - ${stepName}`;
      }),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  // ===========================================
  // STATE UPDATE METHODS
  // ===========================================

  public updatePipelineState(updates: Partial<PipelineState>): void {
    const currentState = this.pipelineStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.pipelineStateSubject.next(newState);

  }

  public updateChatState(updates: Partial<ChatState>): void {
    const currentState = this.chatStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.chatStateSubject.next(newState);

  }

  public updateNavigationState(updates: Partial<NavigationState>): void {
    const currentState = this.navigationStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.navigationStateSubject.next(newState);

  }

  public updateLoadingState(updates: Partial<LoadingState>): void {
    const currentState = this.loadingStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.loadingStateSubject.next(newState);

  }

  public updateErrorState(updates: Partial<ErrorState>): void {
    const currentState = this.errorStateSubject.value;
    const newState = {
      ...currentState,
      ...updates,
      lastErrorTimestamp: updates.pipelineError || updates.stepError || updates.chatError || updates.networkError
        ? new Date()
        : currentState.lastErrorTimestamp
    };
    this.errorStateSubject.next(newState);

  }

  public updateSessionState(updates: Partial<UserSessionState>): void {
    const currentState = this.sessionStateSubject.value;
    const newState = {
      ...currentState,
      ...updates,
      lastActivity: new Date()
    };
    this.sessionStateSubject.next(newState);

  }

  public updateUIState(updates: Partial<UIState>): void {
    const currentState = this.uiStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.uiStateSubject.next(newState);

  }

  // ===========================================
  // PIPELINE-SPECIFIC METHODS
  // ===========================================

  public setPipelineStart(response: PipelineStartResponse): void {
    this.updatePipelineState({
      run_id: response.run_id,
      current_step: response.current_step as PipelineStep,
      completed_steps: []
    });

    this.updateNavigationState({
      currentStepIndex: 0,
      currentStepId: 'understanding',
      completedSteps: [],
      stepsWithData: []
    });

    this.clearAllErrors();
  }

  public setPipelineStepComplete(response: PipelineStepResponse): void {
    const currentPipeline = this.pipelineStateSubject.value;
    const stepData = { [response.step]: response.data };

    this.updatePipelineState({
      current_step: response.step as PipelineStep,
      data: { ...currentPipeline.data, ...stepData },
      completed_steps: [...new Set([...currentPipeline.completed_steps, response.step as PipelineStep])]
    });

    // Update navigation state based on step completion
    this.updateNavigationFromPipelineStep(response.step as PipelineStep);
  }

  public addChatMessage(message: ChatMessage): void {
    const currentChat = this.chatStateSubject.value;
    this.updateChatState({
      messages: [...currentChat.messages, message]
    });
  }

  /**
   * Add a new chat message (not restored from storage)
   */
  public addNewChatMessage(message: Omit<ChatMessage, 'isRestored'>): void {
    const newMessage: ChatMessage = {
      ...message,
      isRestored: false // Mark as new message
    };
    this.addChatMessage(newMessage);
  }

  /**
   * Restore chat messages from storage (prevents typewriter animation)
   */
  public restoreChatMessages(messages: ChatMessage[]): void {
    const restoredMessages = messages.map(msg => ({
      ...msg,
      isRestored: true, // Mark as restored to prevent typewriter animation
      timestamp: new Date(msg.timestamp) // Ensure timestamp is Date object
    }));

    this.updateChatState({
      messages: restoredMessages,
      restoredFromStorage: true
    });


  }

  /**
   * Update current input text in chat state
   */
  public updateChatInputText(text: string): void {
    this.updateChatState({
      currentInputText: text
    });
  }

  /**
   * Clear chat input text
   */
  public clearChatInputText(): void {
    this.updateChatState({
      currentInputText: ''
    });
  }

  public setChatResponse(response: ChatResponse): void {
    this.updateChatState({
      lastResponse: response,
      isAiTyping: false,
      isLoading: false,
      errorMessage: response.response_type === 'error' ? response.message_to_user : null
    });
  }

  // ===========================================
  // ERROR MANAGEMENT
  // ===========================================

  public setError(type: keyof ErrorState, message: string): void {
    this.updateErrorState({ [type]: message });
  }

  public clearError(type: keyof ErrorState): void {
    this.updateErrorState({ [type]: null });
  }

  public clearAllErrors(): void {
    this.updateErrorState({
      pipelineError: null,
      stepError: null,
      chatError: null,
      networkError: null
    });
  }

  // ===========================================
  // LOADING MANAGEMENT
  // ===========================================

  public setLoading(type: keyof LoadingState, isLoading: boolean, message?: string, operation?: string): void {
    const updates: Partial<LoadingState> = { [type]: isLoading };
    if (message !== undefined) {
      updates.loadingMessage = message;
    }
    if (operation !== undefined) {
      updates.currentOperation = isLoading ? operation : null;
    } else if (!isLoading) {
      updates.currentOperation = null;
    }
    this.updateLoadingState(updates);
  }

  public clearAllLoading(): void {
    this.updateLoadingState({
      isLoadingPipeline: false,
      isLoadingStep: false,
      isLoadingChat: false,
      currentOperation: null,
      loadingMessage: null
    });
  }

  // ===========================================
  // UTILITY METHODS
  // ===========================================

  private updateNavigationFromPipelineStep(step: PipelineStep): void {
    const stepToIndexMap: Record<PipelineStep, number> = {
      'market_research': 0,
      'lbc': 0, // Both market_research and lbc show on understanding step
      'persona': 1,
      'swot': 2,
      'features': 3,
      'roadmap': 4
    };

    const stepToIdMap: Record<PipelineStep, string> = {
      'market_research': 'understanding',
      'lbc': 'understanding',
      'persona': 'persona',
      'swot': 'swot',
      'features': 'features',
      'roadmap': 'roadmap'
    };

    const stepIndex = stepToIndexMap[step];
    const stepId = stepToIdMap[step];
    const currentNav = this.navigationStateSubject.value;

    this.updateNavigationState({
      currentStepIndex: stepIndex,
      currentStepId: stepId,
      stepsWithData: [...new Set([...currentNav.stepsWithData, stepId])],
      canGoNext: stepIndex < 4,
      canGoPrevious: stepIndex > 0
    });
  }

  public resetState(): void {
    this.pipelineStateSubject.next(DEFAULT_PIPELINE_STATE);
    this.chatStateSubject.next(DEFAULT_CHAT_STATE);
    this.navigationStateSubject.next(DEFAULT_NAVIGATION_STATE);
    this.loadingStateSubject.next(DEFAULT_LOADING_STATE);
    this.errorStateSubject.next(DEFAULT_ERROR_STATE);
    this.sessionStateSubject.next(DEFAULT_SESSION_STATE);
    this.uiStateSubject.next(DEFAULT_UI_STATE);

    // Clear session storage as well
    this.clearStoredState();

  }

  // ===========================================
  // COMPUTED SELECTORS
  // ===========================================

  public selectComputedState(): Observable<ComputedSelectors> {
    return combineLatest([
      this.pipelineState$,
      this.chatState$,
      this.navigationState$,
      this.errorState$
    ]).pipe(
      map(([pipeline, chat, navigation, error]) => ({
        getCurrentStepData: () => {
          const currentStep = pipeline.current_step;
          return currentStep ? pipeline.data[currentStep] : null;
        },
        getStepProgress: () => {
          const totalSteps = 5;
          return (navigation.completedSteps.length / totalSteps) * 100;
        },
        hasActiveSession: () => !!pipeline.run_id,
        isStepCompleted: (stepId: string) => navigation.completedSteps.includes(stepId),
        getRecentChatMessages: (count: number) => chat.messages.slice(-count),
        getErrorSummary: () => {
          const errors = [error.pipelineError, error.stepError, error.chatError, error.networkError]
            .filter(Boolean);
          return errors.length > 0 ? errors.join('; ') : null;
        }
      })),
      shareReplay(1)
    );
  }

  // ===========================================
  // CONVENIENCE METHODS
  // ===========================================

  public hasStoredData(): boolean {
    // Check both BehaviorSubject state and session storage for data
    const pipeline = this.pipelineStateSubject.value;
    const hasMemoryData = !!(pipeline.run_id && Object.keys(pipeline.data || {}).length > 0);

    // Also check session storage as fallback
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);
        const hasStorageData = !!(parsedState.pipeline?.run_id && Object.keys(parsedState.pipeline?.data || {}).length > 0);
        return hasMemoryData || hasStorageData;
      }
    } catch (error) {
      console.warn('⚠️ Failed to check stored data:', error);
    }

    return hasMemoryData;
  }

  public getStepDataByType<T = any>(stepType: PipelineStep): T | null {
    const pipeline = this.pipelineStateSubject.value;
    return pipeline.data[stepType] as T || null;
  }

  public isStepCompleted(stepId: string): boolean {
    const navigation = this.navigationStateSubject.value;
    return navigation.completedSteps.includes(stepId);
  }

  public hasStepData(stepId: string): boolean {
    const navigation = this.navigationStateSubject.value;
    return navigation.stepsWithData.includes(stepId);
  }

  public getCurrentStepData<T = any>(): T | null {
    const pipeline = this.pipelineStateSubject.value;
    const currentStep = pipeline.current_step;
    return currentStep ? pipeline.data[currentStep] as T : null;
  }

  public getRecentChatMessages(count: number = 10): ChatMessage[] {
    const chat = this.chatStateSubject.value;
    return chat.messages.slice(-count);
  }

  public generateMessageId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  // ===========================================
  // DEBUG METHODS
  // ===========================================

  public logCurrentState(): void {
    console.group('🔍 Current App State');

    console.groupEnd();
  }

  public exportState(): string {
    return JSON.stringify(this.currentState, null, 2);
  }

  public importState(stateJson: string): void {
    try {
      const importedState = JSON.parse(stateJson);

      if (importedState.pipeline) this.pipelineStateSubject.next(importedState.pipeline);
      if (importedState.chat) this.chatStateSubject.next(importedState.chat);
      if (importedState.navigation) this.navigationStateSubject.next(importedState.navigation);
      if (importedState.loading) this.loadingStateSubject.next(importedState.loading);
      if (importedState.error) this.errorStateSubject.next(importedState.error);
      if (importedState.session) this.sessionStateSubject.next(importedState.session);
      if (importedState.ui) this.uiStateSubject.next(importedState.ui);


    } catch (error) {
      console.error('❌ Failed to import state:', error);
    }
  }
}
