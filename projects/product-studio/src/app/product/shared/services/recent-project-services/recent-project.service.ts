// import { Injectable } from '@angular/core';
// import { HttpClient } from '@angular/common/http';
// import { Observable } from 'rxjs';
// import { environment } from '../../../../../environments/environment.prod';

// interface ProjectResponse {
//   status_code: number;
//   projects: Project[];
// }

// export interface Project {
//   project_id: string;
//   project_name: string;
//   project_description: string;
//   project_type: string;
//   last_modified: string;
// }

// @Injectable({
//   providedIn: 'root',
// })
// export class RecentProjectService {
//   private apiUrl = environment.apiUrl;

//   constructor(
//     private http: HttpClient,
//     // private userSignatureService: UserSignatureService
//   ) {}

//   getUserProjects(
//     userSignature?: string,
//     numProjects: number = 10,
//   ): Observable<ProjectResponse> {
//     // If userSignature is not provided, use the UserSignatureService to get it
//     // const signature = userSignature || this.userSignatureService.getUserSignatureSync();

//     const signature = userSignature || '<EMAIL>'; // Replace with actual logic to get user signature

//     const params = {
//       user_signature: signature,
//       num_projects: numProjects.toString(),
//     };

//     return this.http.get<ProjectResponse>(`${this.apiUrl}/project`, { params });
//   }

//   formatDate(dateString: string): string {
//     const date = new Date(dateString);
//     return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
//   }
// }
